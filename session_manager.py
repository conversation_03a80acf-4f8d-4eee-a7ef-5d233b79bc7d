"""
Session State Manager for CV Analyzer Enhancement.

This module provides comprehensive session state management for the Streamlit application,
including analysis results storage, configuration persistence, result history, and memory management.
"""

import streamlit as st
import json
import pickle
import hashlib
from typing import Dict, List, Any, Optional, Union
from datetime import datetime, timedelta
from dataclasses import dataclass, asdict
import logging
import os
import tempfile
from pathlib import Path

from models import AnalysisConfig, EnhancedAssessment, ProcessingStatus

logger = logging.getLogger(__name__)


@dataclass
class AnalysisSession:
    """Represents a single analysis session with metadata."""
    session_id: str
    timestamp: datetime
    job_description: str
    config: AnalysisConfig
    results: List[EnhancedAssessment]
    file_names: List[str]
    processing_time: Optional[float] = None
    status: ProcessingStatus = ProcessingStatus.PENDING


@dataclass
class SessionMetrics:
    """Session performance and usage metrics."""
    total_sessions: int
    total_cvs_processed: int
    average_processing_time: float
    memory_usage_mb: float
    cache_hit_rate: float
    last_cleanup: datetime


class SessionStateManager:
    """
    Comprehensive session state management for the CV Analyzer application.
    
    Handles:
    - Analysis results storage and retrieval
    - Configuration persistence
    - Result history and comparison
    - Memory management and cleanup
    - Session metrics tracking
    """
    
    def __init__(self, max_sessions: int = 10, max_memory_mb: int = 500):
        """
        Initialize the session state manager.
        
        Args:
            max_sessions: Maximum number of analysis sessions to keep in history
            max_memory_mb: Maximum memory usage in MB before triggering cleanup
        """
        self.max_sessions = max_sessions
        self.max_memory_mb = max_memory_mb
        self.session_dir = Path(tempfile.gettempdir()) / "cv_analyzer_sessions"
        self.session_dir.mkdir(exist_ok=True)
        
        # Initialize session state variables
        self._initialize_session_state()
        
        # Perform cleanup on initialization
        self._cleanup_old_sessions()
    
    def _initialize_session_state(self):
        """Initialize all session state variables with proper defaults."""
        
        # Core analysis state
        if 'current_analysis_results' not in st.session_state:
            st.session_state.current_analysis_results = None
        
        if 'current_processing_status' not in st.session_state:
            st.session_state.current_processing_status = ProcessingStatus.PENDING
        
        if 'current_session_id' not in st.session_state:
            st.session_state.current_session_id = None
        
        # File and input state
        if 'uploaded_files_data' not in st.session_state:
            st.session_state.uploaded_files_data = []
        
        if 'job_description' not in st.session_state:
            st.session_state.job_description = ""
        
        if 'analysis_config' not in st.session_state:
            st.session_state.analysis_config = AnalysisConfig()
        
        # History and comparison state
        if 'analysis_history' not in st.session_state:
            st.session_state.analysis_history = []
        
        if 'selected_sessions_for_comparison' not in st.session_state:
            st.session_state.selected_sessions_for_comparison = []
        
        if 'comparison_mode' not in st.session_state:
            st.session_state.comparison_mode = False
        
        # Configuration persistence
        if 'saved_configurations' not in st.session_state:
            st.session_state.saved_configurations = {}
        
        if 'default_config_name' not in st.session_state:
            st.session_state.default_config_name = "default"
        
        # Error and status tracking
        if 'error_history' not in st.session_state:
            st.session_state.error_history = []
        
        if 'processing_logs' not in st.session_state:
            st.session_state.processing_logs = []
        
        # Performance metrics
        if 'session_metrics' not in st.session_state:
            st.session_state.session_metrics = SessionMetrics(
                total_sessions=0,
                total_cvs_processed=0,
                average_processing_time=0.0,
                memory_usage_mb=0.0,
                cache_hit_rate=0.0,
                last_cleanup=datetime.now()
            )
        
        # UI state
        if 'last_analysis_time' not in st.session_state:
            st.session_state.last_analysis_time = None
        
        if 'show_advanced_options' not in st.session_state:
            st.session_state.show_advanced_options = False
        
        if 'selected_result_view' not in st.session_state:
            st.session_state.selected_result_view = "summary"
        
        # App initialization flag
        if 'app_initialized' not in st.session_state:
            st.session_state.app_initialized = True
            logger.info("Session state manager initialized")
    
    def create_new_session(self, job_description: str, config: AnalysisConfig, 
                          file_names: List[str]) -> str:
        """
        Create a new analysis session.
        
        Args:
            job_description: The job description for this analysis
            config: Analysis configuration
            file_names: List of uploaded file names
            
        Returns:
            Session ID for the new session
        """
        # Generate unique session ID
        session_data = f"{job_description}{str(config)}{str(file_names)}{datetime.now().isoformat()}"
        session_id = hashlib.md5(session_data.encode()).hexdigest()[:12]
        
        # Create session object
        session = AnalysisSession(
            session_id=session_id,
            timestamp=datetime.now(),
            job_description=job_description,
            config=config,
            results=[],
            file_names=file_names,
            status=ProcessingStatus.PENDING
        )
        
        # Update current session
        st.session_state.current_session_id = session_id
        st.session_state.current_processing_status = ProcessingStatus.PENDING
        
        # Add to history
        self._add_session_to_history(session)
        
        logger.info(f"Created new analysis session: {session_id}")
        return session_id
    
    def update_session_results(self, session_id: str, results: List[EnhancedAssessment], 
                             processing_time: float = None):
        """
        Update session with analysis results.
        
        Args:
            session_id: Session ID to update
            results: Analysis results
            processing_time: Time taken for processing in seconds
        """
        # Update current session state
        if st.session_state.current_session_id == session_id:
            st.session_state.current_analysis_results = results
            st.session_state.current_processing_status = ProcessingStatus.COMPLETED
            st.session_state.last_analysis_time = datetime.now()
        
        # Update session in history
        for i, session in enumerate(st.session_state.analysis_history):
            if session.session_id == session_id:
                st.session_state.analysis_history[i].results = results
                st.session_state.analysis_history[i].processing_time = processing_time
                st.session_state.analysis_history[i].status = ProcessingStatus.COMPLETED
                break
        
        # Update metrics
        self._update_session_metrics(len(results), processing_time)
        
        # Save session to disk for persistence
        self._save_session_to_disk(session_id)
        
        logger.info(f"Updated session {session_id} with {len(results)} results")
    
    def get_current_session_id(self) -> Optional[str]:
        """
        Get the current session ID.

        Returns:
            Current session ID or None if no session is active
        """
        return st.session_state.current_session_id

    def get_session_by_id(self, session_id: str) -> Optional[AnalysisSession]:
        """
        Retrieve a session by its ID.

        Args:
            session_id: Session ID to retrieve

        Returns:
            AnalysisSession object or None if not found
        """
        for session in st.session_state.analysis_history:
            if session.session_id == session_id:
                return session

        # Try loading from disk if not in memory
        return self._load_session_from_disk(session_id)
    
    def get_recent_sessions(self, limit: int = 5) -> List[AnalysisSession]:
        """
        Get the most recent analysis sessions.
        
        Args:
            limit: Maximum number of sessions to return
            
        Returns:
            List of recent AnalysisSession objects
        """
        sessions = sorted(
            st.session_state.analysis_history,
            key=lambda x: x.timestamp,
            reverse=True
        )
        return sessions[:limit]
    
    def save_configuration(self, name: str, config: AnalysisConfig, 
                          set_as_default: bool = False):
        """
        Save an analysis configuration for reuse.
        
        Args:
            name: Name for the configuration
            config: AnalysisConfig object to save
            set_as_default: Whether to set this as the default configuration
        """
        # Convert config to dictionary for JSON serialization
        config_dict = {
            'gap_threshold_months': config.gap_threshold_months,
            'short_position_threshold_months': config.short_position_threshold_months,
            'job_hopping_threshold': config.job_hopping_threshold,
            'recent_experience_years': config.recent_experience_years,
            'skill_weights': config.skill_weights,
            'mandatory_skills': config.mandatory_skills,
            'preferred_skills': config.preferred_skills,
            'saved_timestamp': datetime.now().isoformat()
        }
        
        st.session_state.saved_configurations[name] = config_dict
        
        if set_as_default:
            st.session_state.default_config_name = name
        
        # Persist to disk
        self._save_configurations_to_disk()
        
        logger.info(f"Saved configuration '{name}' (default: {set_as_default})")
    
    def load_configuration(self, name: str) -> Optional[AnalysisConfig]:
        """
        Load a saved configuration.
        
        Args:
            name: Name of the configuration to load
            
        Returns:
            AnalysisConfig object or None if not found
        """
        if name not in st.session_state.saved_configurations:
            return None
        
        config_dict = st.session_state.saved_configurations[name]
        
        # Create AnalysisConfig from dictionary
        config = AnalysisConfig(
            gap_threshold_months=config_dict.get('gap_threshold_months', 3),
            short_position_threshold_months=config_dict.get('short_position_threshold_months', 6),
            job_hopping_threshold=config_dict.get('job_hopping_threshold', 3),
            recent_experience_years=config_dict.get('recent_experience_years', 5),
            skill_weights=config_dict.get('skill_weights', {}),
            mandatory_skills=config_dict.get('mandatory_skills', []),
            preferred_skills=config_dict.get('preferred_skills', [])
        )
        
        logger.info(f"Loaded configuration '{name}'")
        return config
    
    def delete_configuration(self, name: str):
        """
        Delete a saved configuration.
        
        Args:
            name: Name of the configuration to delete
        """
        if name in st.session_state.saved_configurations:
            del st.session_state.saved_configurations[name]
            
            # Update default if necessary
            if st.session_state.default_config_name == name:
                st.session_state.default_config_name = "default"
            
            self._save_configurations_to_disk()
            logger.info(f"Deleted configuration '{name}'")
    
    def get_saved_configuration_names(self) -> List[str]:
        """
        Get list of saved configuration names.
        
        Returns:
            List of configuration names
        """
        return list(st.session_state.saved_configurations.keys())
    
    def enable_comparison_mode(self, session_ids: List[str]):
        """
        Enable comparison mode for multiple sessions.
        
        Args:
            session_ids: List of session IDs to compare
        """
        st.session_state.comparison_mode = True
        st.session_state.selected_sessions_for_comparison = session_ids
        logger.info(f"Enabled comparison mode for {len(session_ids)} sessions")
    
    def disable_comparison_mode(self):
        """Disable comparison mode."""
        st.session_state.comparison_mode = False
        st.session_state.selected_sessions_for_comparison = []
        logger.info("Disabled comparison mode")
    
    def add_processing_log(self, message: str, level: str = "INFO"):
        """
        Add a processing log entry.
        
        Args:
            message: Log message
            level: Log level (INFO, WARNING, ERROR)
        """
        log_entry = {
            'timestamp': datetime.now().isoformat(),
            'level': level,
            'message': message
        }
        
        st.session_state.processing_logs.append(log_entry)
        
        # Keep only recent logs to prevent memory bloat
        if len(st.session_state.processing_logs) > 100:
            st.session_state.processing_logs = st.session_state.processing_logs[-50:]
    
    def add_error(self, error: str, context: Dict[str, Any] = None):
        """
        Add an error to the error history.
        
        Args:
            error: Error message
            context: Additional context information
        """
        error_entry = {
            'timestamp': datetime.now().isoformat(),
            'error': error,
            'context': context or {},
            'session_id': st.session_state.current_session_id
        }
        
        st.session_state.error_history.append(error_entry)
        
        # Keep only recent errors
        if len(st.session_state.error_history) > 50:
            st.session_state.error_history = st.session_state.error_history[-25:]
        
        logger.error(f"Added error to history: {error}")
    
    def get_memory_usage(self) -> float:
        """
        Estimate current memory usage in MB.
        
        Returns:
            Estimated memory usage in MB
        """
        try:
            import sys
            
            # Calculate size of session state objects
            total_size = 0
            
            # Analysis results
            if st.session_state.current_analysis_results:
                total_size += sys.getsizeof(st.session_state.current_analysis_results)
            
            # History
            total_size += sys.getsizeof(st.session_state.analysis_history)
            
            # Configurations
            total_size += sys.getsizeof(st.session_state.saved_configurations)
            
            # Logs and errors
            total_size += sys.getsizeof(st.session_state.processing_logs)
            total_size += sys.getsizeof(st.session_state.error_history)
            
            return total_size / (1024 * 1024)  # Convert to MB
            
        except Exception as e:
            logger.warning(f"Could not calculate memory usage: {e}")
            return 0.0
    
    def cleanup_memory(self, force: bool = False):
        """
        Perform memory cleanup by removing old sessions and data.
        
        Args:
            force: Force cleanup regardless of memory usage
        """
        current_memory = self.get_memory_usage()
        
        if not force and current_memory < self.max_memory_mb:
            return
        
        logger.info(f"Starting memory cleanup (current usage: {current_memory:.1f}MB)")
        
        # Remove old sessions beyond the limit
        if len(st.session_state.analysis_history) > self.max_sessions:
            # Sort by timestamp and keep only the most recent
            st.session_state.analysis_history = sorted(
                st.session_state.analysis_history,
                key=lambda x: x.timestamp,
                reverse=True
            )[:self.max_sessions]
        
        # Clean up old processing logs
        if len(st.session_state.processing_logs) > 50:
            st.session_state.processing_logs = st.session_state.processing_logs[-25:]
        
        # Clean up old errors
        if len(st.session_state.error_history) > 25:
            st.session_state.error_history = st.session_state.error_history[-10:]
        
        # Update metrics
        st.session_state.session_metrics.last_cleanup = datetime.now()
        st.session_state.session_metrics.memory_usage_mb = self.get_memory_usage()
        
        logger.info(f"Memory cleanup completed (new usage: {st.session_state.session_metrics.memory_usage_mb:.1f}MB)")
    
    def reset_session(self):
        """Reset the current session state."""
        st.session_state.current_analysis_results = None
        st.session_state.current_processing_status = ProcessingStatus.PENDING
        st.session_state.current_session_id = None
        st.session_state.uploaded_files_data = []
        st.session_state.job_description = ""
        st.session_state.comparison_mode = False
        st.session_state.selected_sessions_for_comparison = []
        
        logger.info("Session state reset")
    
    def export_session_data(self, session_id: str) -> Dict[str, Any]:
        """
        Export session data for backup or analysis.
        
        Args:
            session_id: Session ID to export
            
        Returns:
            Dictionary containing session data
        """
        session = self.get_session_by_id(session_id)
        if not session:
            return {}
        
        return {
            'session_id': session.session_id,
            'timestamp': session.timestamp.isoformat(),
            'job_description': session.job_description,
            'config': asdict(session.config),
            'file_names': session.file_names,
            'processing_time': session.processing_time,
            'status': session.status.value,
            'results_count': len(session.results),
            'results': [asdict(result) for result in session.results] if session.results else []
        }
    
    def _add_session_to_history(self, session: AnalysisSession):
        """Add a session to the history."""
        st.session_state.analysis_history.append(session)
        
        # Maintain history size limit
        if len(st.session_state.analysis_history) > self.max_sessions:
            st.session_state.analysis_history = st.session_state.analysis_history[-self.max_sessions:]
    
    def _update_session_metrics(self, num_cvs: int, processing_time: float = None):
        """Update session performance metrics."""
        metrics = st.session_state.session_metrics
        
        metrics.total_sessions += 1
        metrics.total_cvs_processed += num_cvs
        
        if processing_time:
            # Update average processing time
            total_time = metrics.average_processing_time * (metrics.total_sessions - 1) + processing_time
            metrics.average_processing_time = total_time / metrics.total_sessions
        
        metrics.memory_usage_mb = self.get_memory_usage()
    
    def _save_session_to_disk(self, session_id: str):
        """Save session data to disk for persistence."""
        try:
            session = self.get_session_by_id(session_id)
            if not session:
                return
            
            session_file = self.session_dir / f"{session_id}.pkl"
            with open(session_file, 'wb') as f:
                pickle.dump(session, f)
                
        except Exception as e:
            logger.warning(f"Could not save session {session_id} to disk: {e}")
    
    def _load_session_from_disk(self, session_id: str) -> Optional[AnalysisSession]:
        """Load session data from disk."""
        try:
            session_file = self.session_dir / f"{session_id}.pkl"
            if session_file.exists():
                with open(session_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            logger.warning(f"Could not load session {session_id} from disk: {e}")
        
        return None
    
    def _save_configurations_to_disk(self):
        """Save configurations to disk for persistence."""
        try:
            config_file = self.session_dir / "configurations.json"
            with open(config_file, 'w') as f:
                json.dump(st.session_state.saved_configurations, f, indent=2)
        except Exception as e:
            logger.warning(f"Could not save configurations to disk: {e}")
    
    def _load_configurations_from_disk(self):
        """Load configurations from disk."""
        try:
            config_file = self.session_dir / "configurations.json"
            if config_file.exists():
                with open(config_file, 'r') as f:
                    st.session_state.saved_configurations = json.load(f)
        except Exception as e:
            logger.warning(f"Could not load configurations from disk: {e}")
    
    def _cleanup_old_sessions(self):
        """Clean up old session files from disk."""
        try:
            cutoff_date = datetime.now() - timedelta(days=7)  # Keep sessions for 7 days
            
            for session_file in self.session_dir.glob("*.pkl"):
                if session_file.stat().st_mtime < cutoff_date.timestamp():
                    session_file.unlink()
                    logger.debug(f"Cleaned up old session file: {session_file}")
                    
        except Exception as e:
            logger.warning(f"Error during session cleanup: {e}")


# Global session manager instance
session_manager = SessionStateManager()