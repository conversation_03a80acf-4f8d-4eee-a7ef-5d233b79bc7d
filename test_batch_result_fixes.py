#!/usr/bin/env python3
"""
Test script to verify BatchProcessingResult attribute fixes.
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from models import BatchProcessingResult, <PERSON>hanced<PERSON><PERSON><PERSON><PERSON>, ProcessingStatus, AnalysisConfig
from session_manager import SessionStateManager, AnalysisSession
from datetime import datetime
import streamlit as st

def test_batch_processing_result_attributes():
    """Test that BatchProcessingResult has the correct attributes."""
    print("Testing BatchProcessingResult attributes...")
    
    # Create a sample assessment
    assessment = EnhancedAssessment(
        candidate_name="Test Candidate",
        match_score=0.85,
        processing_status=ProcessingStatus.COMPLETED,
        analysis_timestamp=datetime.now()
    )
    
    # Create BatchProcessingResult
    batch_result = BatchProcessingResult(
        total_processed=1,
        successful_count=1,
        failed_count=0,
        processing_time_seconds=30.0
    )
    batch_result.assessments = [assessment]
    
    # Test attributes that were causing errors
    print(f"✅ total_processed: {batch_result.total_processed}")
    print(f"✅ successful_count: {batch_result.successful_count}")
    print(f"✅ failed_count: {batch_result.failed_count}")
    print(f"✅ processing_time_seconds: {batch_result.processing_time_seconds}")
    print(f"✅ assessments count: {len(batch_result.assessments)}")
    
    # Test that total_count attribute does NOT exist (this was the error)
    try:
        _ = batch_result.total_count
        print("❌ ERROR: total_count attribute should not exist!")
        return False
    except AttributeError:
        print("✅ Confirmed: total_count attribute correctly does not exist")
    
    return True

def test_session_manager_batch_result():
    """Test that session manager correctly handles BatchProcessingResult."""
    print("\nTesting session manager with BatchProcessingResult...")
    
    # Mock streamlit session state
    class MockSessionState:
        def __init__(self):
            self.current_session_id = None
            self.current_analysis_results = None
            self.current_processing_status = ProcessingStatus.PENDING
            self.analysis_history = []
            self.session_metrics = type('obj', (object,), {
                'total_sessions': 0,
                'total_analyses': 0,
                'total_processing_time': 0.0,
                'memory_usage_mb': 0.0
            })()
            self.last_analysis_time = None
    
    # Mock streamlit
    st.session_state = MockSessionState()
    
    # Create session manager
    session_manager = SessionStateManager()
    
    # Create test data
    config = AnalysisConfig()
    assessment = EnhancedAssessment(
        candidate_name="Test Candidate",
        match_score=0.85,
        processing_status=ProcessingStatus.COMPLETED,
        analysis_timestamp=datetime.now()
    )
    
    batch_result = BatchProcessingResult(
        total_processed=1,
        successful_count=1,
        failed_count=0,
        processing_time_seconds=30.0
    )
    batch_result.assessments = [assessment]
    
    # Create session
    session_id = session_manager.create_session(
        job_description="Test job",
        config=config,
        file_names=["test.pdf"]
    )
    
    # Update session with BatchProcessingResult
    session_manager.update_session_results(session_id, batch_result)
    
    # Verify the results are stored correctly
    if st.session_state.current_analysis_results is None:
        print("❌ ERROR: current_analysis_results is None")
        return False
    
    if not isinstance(st.session_state.current_analysis_results, BatchProcessingResult):
        print(f"❌ ERROR: current_analysis_results is {type(st.session_state.current_analysis_results)}, expected BatchProcessingResult")
        return False
    
    # Test accessing attributes that were causing errors
    results = st.session_state.current_analysis_results
    print(f"✅ Stored total_processed: {results.total_processed}")
    print(f"✅ Stored successful_count: {results.successful_count}")
    print(f"✅ Stored processing_time_seconds: {results.processing_time_seconds}")
    
    # Test session loading
    session = session_manager.get_session_by_id(session_id)
    if session and session.results:
        print(f"✅ Session results type: {type(session.results)}")
        print(f"✅ Session results total_processed: {session.results.total_processed}")
    else:
        print("❌ ERROR: Session results not found or None")
        return False
    
    return True

def test_dashboard_compatibility():
    """Test that the dashboard can handle BatchProcessingResult correctly."""
    print("\nTesting dashboard compatibility...")
    
    # Create test BatchProcessingResult
    assessment = EnhancedAssessment(
        candidate_name="Test Candidate",
        match_score=0.85,
        processing_status=ProcessingStatus.COMPLETED,
        analysis_timestamp=datetime.now()
    )
    
    batch_result = BatchProcessingResult(
        total_processed=2,
        successful_count=1,
        failed_count=1,
        processing_time_seconds=45.0
    )
    batch_result.assessments = [assessment]
    
    # Test the operations that the dashboard performs
    try:
        # These are the operations that were failing
        total = batch_result.total_processed
        success_rate = batch_result.successful_count / batch_result.total_processed if batch_result.total_processed > 0 else 0
        failure_rate = batch_result.failed_count / batch_result.total_processed if batch_result.total_processed > 0 else 0
        
        print(f"✅ Total processed: {total}")
        print(f"✅ Success rate: {success_rate:.1%}")
        print(f"✅ Failure rate: {failure_rate:.1%}")
        print(f"✅ Processing time: {batch_result.processing_time_seconds:.1f}s")
        print(f"✅ Assessments count: {len(batch_result.assessments)}")
        
        return True
    except Exception as e:
        print(f"❌ ERROR in dashboard operations: {e}")
        return False

def main():
    """Run all tests."""
    print("🧪 Testing BatchProcessingResult fixes...\n")
    
    tests = [
        test_batch_processing_result_attributes,
        test_session_manager_batch_result,
        test_dashboard_compatibility
    ]
    
    passed = 0
    for test in tests:
        try:
            if test():
                passed += 1
                print("✅ PASSED\n")
            else:
                print("❌ FAILED\n")
        except Exception as e:
            print(f"❌ FAILED with exception: {e}\n")
    
    print(f"📊 Results: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! The BatchProcessingResult fixes are working correctly.")
        return True
    else:
        print("💥 Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
