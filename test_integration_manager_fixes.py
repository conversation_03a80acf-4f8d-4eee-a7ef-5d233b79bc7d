#!/usr/bin/env python3
"""
Test script for integration manager fixes.
Tests the specific fixes for progress tracking and session manager health.
"""

import sys
import os
from unittest.mock import Mock, patch, MagicMock

# Add current directory to path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from integration_manager import IntegrationManager
from progress_tracker import CVAnalysisProgressTracker, TaskStatus
from session_manager import SessionStateManager
from models import AnalysisConfig

def test_integration_manager_fixes():
    """Test the integration manager with our fixes."""
    print("Testing integration manager fixes...")
    
    try:
        # Mock dependencies
        with patch('integration_manager.AnalysisEngine') as mock_engine_class, \
             patch('integration_manager.PDFTextCache') as mock_pdf_cache_class, \
             patch('integration_manager.LLMResponseCache') as mock_llm_cache_class, \
             patch('integration_manager.ExportService') as mock_export_class:
            
            # Setup mocks
            mock_engine = Mock()
            mock_engine_class.return_value = mock_engine
            
            mock_pdf_cache = Mock()
            mock_pdf_cache_class.return_value = mock_pdf_cache
            
            mock_llm_cache = Mock()
            mock_llm_cache_class.return_value = mock_llm_cache
            
            mock_export = Mock()
            mock_export_class.return_value = mock_export
            
            # Create integration manager
            integration_manager = IntegrationManager()
            print("✅ Successfully created IntegrationManager")

            # Initialize the system
            config = AnalysisConfig()
            with patch.dict(os.environ, {'GROQ_API_KEY': 'test_key'}):
                success = integration_manager.initialize_system(config)
            print(f"✅ System initialization: {success}")

            # Test 1: Progress tracker callback functionality
            print("\n--- Testing Progress Tracker Callback ---")

            # Create a progress tracker (like the integration manager does)
            progress_tracker = CVAnalysisProgressTracker(2)
            print(f"✅ Progress tracker type: {type(progress_tracker).__name__}")
            
            # Test that add_progress_callback method exists
            assert hasattr(progress_tracker, 'add_progress_callback'), "Missing add_progress_callback method"
            print("✅ add_progress_callback method exists")
            
            # Test adding a callback
            callback_calls = []
            def test_callback(progress_data):
                callback_calls.append(progress_data)
                print(f"  📊 Progress callback: {progress_data.get('overall_progress', 0):.1%}")
            
            progress_tracker.add_progress_callback(test_callback)
            print("✅ Successfully added progress callback")
            
            # Test progress updates trigger callbacks
            progress_tracker.start_tracking()
            progress_tracker.update_step_status('initialization', progress_tracker.TaskStatus.RUNNING)
            progress_tracker.update_step_status('initialization', progress_tracker.TaskStatus.COMPLETED)
            
            assert len(callback_calls) > 0, "No callbacks were triggered"
            print(f"✅ Progress callbacks triggered: {len(callback_calls)} times")
            
            # Test 2: Session manager health check
            print("\n--- Testing Session Manager Health Check ---")

            # Get the session manager
            session_manager = integration_manager.components.get('session_manager')
            print(f"✅ Session manager type: {type(session_manager).__name__}")
            
            # Test that get_current_session_id method exists
            assert hasattr(session_manager, 'get_current_session_id'), "Missing get_current_session_id method"
            print("✅ get_current_session_id method exists")
            
            # Test calling the method
            session_id = session_manager.get_current_session_id()
            print(f"✅ get_current_session_id() returned: {session_id}")
            
            # Test 3: Health check logic (similar to integration_manager._run_health_checks)
            print("\n--- Testing Health Check Logic ---")
            
            # Test progress tracker health
            progress_healthy = (
                progress_tracker is not None and
                hasattr(progress_tracker, 'add_progress_callback')
            )
            print(f"✅ Progress tracker healthy: {progress_healthy}")

            # Test session manager health
            session_healthy = (
                session_manager is not None and
                hasattr(session_manager, 'get_current_session_id')
            )
            print(f"✅ Session manager healthy: {session_healthy}")
            
            # Overall health
            overall_healthy = progress_healthy and session_healthy
            print(f"✅ Overall system healthy: {overall_healthy}")
            
            # Test 4: Simulate the original error scenario
            print("\n--- Testing Original Error Scenario ---")
            
            # This should NOT raise AttributeError anymore
            try:
                # Simulate what integration_manager.py line 263 does
                def dummy_progress_callback(progress_data):
                    pass

                progress_tracker.add_progress_callback(dummy_progress_callback)
                print("✅ Original error scenario resolved - no AttributeError")
                
            except AttributeError as e:
                print(f"❌ Original error still exists: {e}")
                return False
            
            print("\n🎉 All integration manager fixes working correctly!")
            return True
            
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    try:
        success = test_integration_manager_fixes()
        sys.exit(0 if success else 1)
    except Exception as e:
        print(f"❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
