"""
Streamlit Web Interface for CV Analyzer Enhancement.

This module provides a user-friendly web interface for analyzing candidate CVs
against job descriptions using advanced AI-powered analysis capabilities.
"""

import streamlit as st
import asyncio
import tempfile
import os
from typing import List, Dict, Any, Optional
from datetime import datetime
import logging
import pandas as pd
import plotly.express as px
import plotly.graph_objects as go
from plotly.subplots import make_subplots
from collections import Counter
from PyPDF2 import PdfReader
import io

# Load environment variables early
from dotenv import load_dotenv
load_dotenv()

# Import our analysis components
from models import AnalysisConfig, EnhancedAssessment, ProcessingStatus, ExportConfig
from analysis_engine import AnalysisEngine
from export_service import ExportService
from session_manager import session_manager
from progress_tracker import CVAnalysisProgressTracker, TaskStatus, set_current_tracker, clear_current_tracker

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Page configuration
st.set_page_config(
    page_title="CV Analyzer Enhancement",
    page_icon="📄",
    layout="wide",
    initial_sidebar_state="expanded"
)

# Custom CSS for better styling
st.markdown("""
<style>
    .main-header {
        font-size: 2.5rem;
        font-weight: bold;
        color: #1f77b4;
        text-align: center;
        margin-bottom: 2rem;
    }
    .section-header {
        font-size: 1.5rem;
        font-weight: bold;
        color: #2c3e50;
        margin-top: 2rem;
        margin-bottom: 1rem;
        border-bottom: 2px solid #3498db;
        padding-bottom: 0.5rem;
    }
    .upload-section {
        background-color: #f8f9fa;
        padding: 1.5rem;
        border-radius: 10px;
        border: 2px dashed #3498db;
        margin: 1rem 0;
    }
    .job-description-section {
        background-color: #f1f8ff;
        padding: 1.5rem;
        border-radius: 10px;
        border-left: 4px solid #3498db;
        margin: 1rem 0;
    }
    .error-message {
        background-color: #fee;
        color: #c33;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #c33;
        margin: 1rem 0;
    }
    .success-message {
        background-color: #efe;
        color: #363;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #363;
        margin: 1rem 0;
    }
    .info-message {
        background-color: #e7f3ff;
        color: #0066cc;
        padding: 1rem;
        border-radius: 5px;
        border-left: 4px solid #0066cc;
        margin: 1rem 0;
    }
</style>
""", unsafe_allow_html=True)


def initialize_session_state():
    """Initialize Streamlit session state variables using the session manager."""
    # Initialize session manager first
    session_manager._initialize_session_state()

    # Initialize processing status specifically (this was missing!)
    if 'processing_status' not in st.session_state:
        st.session_state.processing_status = None

    # Initialize other critical variables that might be missing
    if 'analysis_results' not in st.session_state:
        st.session_state.analysis_results = None

    if 'export_data' not in st.session_state:
        st.session_state.export_data = None

    if 'system_initialized' not in st.session_state:
        st.session_state.system_initialized = False

    if 'integration_manager' not in st.session_state:
        st.session_state.integration_manager = None

    # Initialize skill text areas for configuration
    if 'mandatory_skills_text' not in st.session_state:
        st.session_state.mandatory_skills_text = ""

    if 'preferred_skills_text' not in st.session_state:
        st.session_state.preferred_skills_text = ""


def check_system_status() -> Dict[str, Any]:
    """
    Check system status and environment configuration.
    
    Returns:
        Dictionary with system status information
    """
    status = {
        'api_key_configured': bool(os.getenv('GROQ_API_KEY')),
        'environment_ready': True,
        'warnings': [],
        'errors': []
    }
    
    # Check API key
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        status['errors'].append("GROQ_API_KEY environment variable not set")
        status['environment_ready'] = False
    elif len(api_key.strip()) < 10:
        status['warnings'].append("GROQ_API_KEY appears to be too short")
    
    # Check Python version
    import sys
    python_version = sys.version_info
    if python_version < (3, 8):
        status['warnings'].append(f"Python {python_version.major}.{python_version.minor} detected. Python 3.8+ recommended")
    
    # Check available memory
    try:
        import psutil
        memory = psutil.virtual_memory()
        available_gb = memory.available / (1024**3)
        
        if available_gb < 1:
            status['warnings'].append(f"Low available memory: {available_gb:.1f}GB")
        
        status['memory_available_gb'] = available_gb
        status['memory_usage_percent'] = memory.percent
    except ImportError:
        status['warnings'].append("psutil not available - cannot check memory status")
    
    # Check disk space
    try:
        import shutil
        disk_usage = shutil.disk_usage('.')
        free_gb = disk_usage.free / (1024**3)
        
        if free_gb < 1:
            status['warnings'].append(f"Low disk space: {free_gb:.1f}GB available")
        
        status['disk_free_gb'] = free_gb
    except Exception:
        status['warnings'].append("Cannot check disk space")
    
    return status


def render_system_status():
    """Render system status information in the sidebar."""
    with st.sidebar:
        st.markdown("### 🔧 System Status")
        
        status = check_system_status()
        
        # Overall status indicator
        if status['environment_ready'] and not status['errors']:
            st.success("✅ System Ready")
        elif status['errors']:
            st.error("❌ Configuration Issues")
        else:
            st.warning("⚠️ Warnings Present")
        
        # API Status
        if status['api_key_configured']:
            st.write("🔑 API Key: ✅ Configured")
        else:
            st.write("🔑 API Key: ❌ Missing")
        
        # Memory status
        if 'memory_available_gb' in status:
            memory_color = "🟢" if status['memory_available_gb'] > 2 else "🟡" if status['memory_available_gb'] > 1 else "🔴"
            st.write(f"💾 Memory: {memory_color} {status['memory_available_gb']:.1f}GB available")
        
        # Disk status
        if 'disk_free_gb' in status:
            disk_color = "🟢" if status['disk_free_gb'] > 5 else "🟡" if status['disk_free_gb'] > 1 else "🔴"
            st.write(f"💿 Disk: {disk_color} {status['disk_free_gb']:.1f}GB free")
        
        # Show errors and warnings
        if status['errors']:
            with st.expander("❌ Errors", expanded=True):
                for error in status['errors']:
                    st.error(error)
        
        if status['warnings']:
            with st.expander("⚠️ Warnings", expanded=False):
                for warning in status['warnings']:
                    st.warning(warning)
        
        # Last analysis info
        if st.session_state.last_analysis_time:
            st.write(f"🕒 Last Analysis: {st.session_state.last_analysis_time.strftime('%H:%M:%S')}")


def render_help_section():
    """Render help and documentation section in the sidebar."""
    with st.sidebar:
        st.markdown("### 📚 Help & Documentation")
        
        with st.expander("🚀 Quick Start Guide", expanded=False):
            st.markdown("""
            **1. Upload CVs**
            - Select PDF files (max 10MB each)
            - Multiple files supported
            
            **2. Enter Job Description**
            - Include required skills
            - Specify experience level
            - Add responsibilities
            
            **3. Start Analysis**
            - Click "Analyze CVs" button
            - Wait for processing
            - Review results
            """)
        
        with st.expander("💡 Tips for Best Results", expanded=False):
            st.markdown("""
            **CV Files:**
            - Use text-based PDFs (not scanned images)
            - Ensure clear section headers
            - Include complete work history
            
            **Job Descriptions:**
            - Be specific about required skills
            - Include experience levels (e.g., "5+ years")
            - Separate required vs. preferred skills
            - Add context about the role
            """)
        
        with st.expander("🔧 Troubleshooting", expanded=False):
            st.markdown("""
            **Common Issues:**
            - **API Key Error:** Set GROQ_API_KEY environment variable
            - **File Upload Failed:** Check file size (<10MB) and format (PDF)
            - **Analysis Stuck:** Check internet connection and API limits
            - **Poor Results:** Improve job description detail
            
            **Need Help?**
            - Check system status above
            - Review validation messages
            - Contact support if issues persist
            """)


def render_session_management_sidebar():
    """Render session management controls in the sidebar."""
    with st.sidebar:
        st.markdown("### 📊 Session Management")
        
        # Current session info
        if st.session_state.current_session_id:
            st.info(f"🔄 Current Session: {st.session_state.current_session_id[:8]}...")
            
            # Session actions
            col1, col2 = st.columns(2)
            with col1:
                if st.button("🔄 New Session", help="Start a new analysis session"):
                    session_manager.reset_session()
                    st.rerun()
            
            with col2:
                if st.button("💾 Save Config", help="Save current configuration"):
                    render_save_configuration_dialog()
        
        # Recent sessions
        recent_sessions = session_manager.get_recent_sessions(5)
        if recent_sessions:
            st.markdown("#### 📋 Recent Sessions")
            
            for session in recent_sessions:
                with st.expander(f"📄 {session.timestamp.strftime('%m/%d %H:%M')}", expanded=False):
                    st.write(f"**Files:** {len(session.file_names)}")
                    st.write(f"**Status:** {session.status.value}")
                    if session.processing_time:
                        st.write(f"**Time:** {session.processing_time:.1f}s")
                    
                    col1, col2 = st.columns(2)
                    with col1:
                        if st.button("📂 Load", key=f"load_{session.session_id}"):
                            load_session(session.session_id)
                    with col2:
                        if st.button("📊 Compare", key=f"compare_{session.session_id}"):
                            add_to_comparison(session.session_id)
        
        # Comparison mode
        if st.session_state.comparison_mode:
            st.markdown("#### 🔍 Comparison Mode")
            st.info(f"Comparing {len(st.session_state.selected_sessions_for_comparison)} sessions")
            
            if st.button("❌ Exit Comparison"):
                session_manager.disable_comparison_mode()
                st.rerun()
        
        # Memory usage
        memory_usage = session_manager.get_memory_usage()
        if memory_usage > 0:
            st.markdown("#### 💾 Memory Usage")
            st.progress(min(1.0, memory_usage / session_manager.max_memory_mb))
            st.write(f"{memory_usage:.1f} MB / {session_manager.max_memory_mb} MB")
            
            if memory_usage > session_manager.max_memory_mb * 0.8:
                if st.button("🧹 Cleanup Memory"):
                    session_manager.cleanup_memory(force=True)
                    st.success("Memory cleaned up!")
                    st.rerun()


def render_configuration_management():
    """Render configuration management interface."""
    st.markdown("### ⚙️ Configuration Management")
    
    # Load saved configurations
    saved_configs = session_manager.get_saved_configuration_names()
    
    if saved_configs:
        col1, col2, col3 = st.columns([2, 1, 1])
        
        with col1:
            selected_config = st.selectbox(
                "Load Saved Configuration",
                options=[""] + saved_configs,
                help="Select a previously saved configuration to load"
            )
        
        with col2:
            if st.button("📂 Load Config") and selected_config:
                config = session_manager.load_configuration(selected_config)
                if config:
                    st.session_state.analysis_config = config
                    st.success(f"Loaded configuration: {selected_config}")
                    st.rerun()
        
        with col3:
            if st.button("🗑️ Delete Config") and selected_config:
                session_manager.delete_configuration(selected_config)
                st.success(f"Deleted configuration: {selected_config}")
                st.rerun()
    
    # Save current configuration
    st.markdown("#### 💾 Save Current Configuration")
    col1, col2 = st.columns([2, 1])
    
    with col1:
        config_name = st.text_input(
            "Configuration Name",
            placeholder="e.g., Senior Developer, Data Scientist",
            help="Enter a name for this configuration"
        )
    
    with col2:
        set_default = st.checkbox("Set as Default", help="Use this configuration by default")
    
    if st.button("💾 Save Configuration") and config_name:
        session_manager.save_configuration(
            config_name, 
            st.session_state.analysis_config, 
            set_default
        )
        st.success(f"Configuration '{config_name}' saved!")


def render_session_history():
    """Render session history and comparison interface."""
    st.markdown("### 📊 Analysis History")
    
    sessions = session_manager.get_recent_sessions(10)
    
    if not sessions:
        st.info("No previous analysis sessions found.")
        return
    
    # Session comparison selection
    st.markdown("#### 🔍 Select Sessions for Comparison")
    
    selected_sessions = []
    for session in sessions:
        col1, col2, col3, col4, col5 = st.columns([1, 2, 2, 1, 1])
        
        with col1:
            if st.checkbox("", key=f"select_{session.session_id}"):
                selected_sessions.append(session.session_id)
        
        with col2:
            st.write(session.timestamp.strftime("%m/%d/%Y %H:%M"))
        
        with col3:
            st.write(f"{len(session.file_names)} CVs")
        
        with col4:
            status_color = {
                ProcessingStatus.COMPLETED: "🟢",
                ProcessingStatus.FAILED: "🔴",
                ProcessingStatus.PENDING: "🟡"
            }
            st.write(f"{status_color.get(session.status, '⚪')} {session.status.value}")
        
        with col5:
            if session.processing_time:
                st.write(f"{session.processing_time:.1f}s")
    
    # Comparison controls
    if len(selected_sessions) > 1:
        if st.button("🔍 Compare Selected Sessions"):
            session_manager.enable_comparison_mode(selected_sessions)
            st.success(f"Comparison mode enabled for {len(selected_sessions)} sessions")
            st.rerun()
    
    # Session metrics
    metrics = st.session_state.session_metrics
    if metrics.total_sessions > 0:
        st.markdown("#### 📈 Session Metrics")
        
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Total Sessions", metrics.total_sessions)
        with col2:
            st.metric("CVs Processed", metrics.total_cvs_processed)
        with col3:
            st.metric("Avg Processing Time", f"{metrics.average_processing_time:.1f}s")
        with col4:
            st.metric("Memory Usage", f"{metrics.memory_usage_mb:.1f} MB")


def render_processing_logs():
    """Render processing logs and error history."""
    st.markdown("### 📝 Processing Logs")
    
    # Recent logs
    if st.session_state.processing_logs:
        st.markdown("#### 🔍 Recent Activity")
        
        for log in reversed(st.session_state.processing_logs[-10:]):
            timestamp = datetime.fromisoformat(log['timestamp']).strftime("%H:%M:%S")
            level_color = {
                "INFO": "🔵",
                "WARNING": "🟡", 
                "ERROR": "🔴"
            }
            
            st.write(f"{level_color.get(log['level'], '⚪')} {timestamp} - {log['message']}")
    
    # Error history
    if st.session_state.error_history:
        st.markdown("#### ❌ Error History")
        
        with st.expander("View Error Details", expanded=False):
            for error in reversed(st.session_state.error_history[-5:]):
                timestamp = datetime.fromisoformat(error['timestamp']).strftime("%m/%d %H:%M:%S")
                st.error(f"**{timestamp}**: {error['error']}")
                
                if error.get('context'):
                    st.json(error['context'])


async def run_analysis_with_session(file_data: List[Dict[str, Any]], 
                                   job_description: str, 
                                   config: AnalysisConfig,
                                   session_id: str):
    """
    Run CV analysis with session management and progress tracking integration.
    
    Args:
        file_data: List of uploaded file data
        job_description: Job description text
        config: Analysis configuration
        session_id: Session ID for tracking
    """
    from integration_manager import get_integration_manager, initialize_integrated_system
    
    start_time = datetime.now()
    
    # Initialize progress tracker
    progress_tracker = CVAnalysisProgressTracker(len(file_data))
    set_current_tracker(progress_tracker)
    
    # Set up cancellation callback
    def cancel_analysis():
        session_manager.add_processing_log("Analysis cancelled by user")
        # In real implementation, this would stop the analysis engine
        pass
    
    progress_tracker.set_cancellation_callback(cancel_analysis)
    progress_tracker.start_tracking()
    
    try:
        # Step 1: Initialize integrated system
        progress_tracker.update_step_status("initialization", TaskStatus.RUNNING)
        session_manager.add_processing_log(f"Initializing integrated system for session {session_id}")
        
        integration_mgr = get_integration_manager()
        if not integration_mgr.is_initialized:
            system_initialized = initialize_integrated_system(config)
            if not system_initialized:
                raise RuntimeError("Failed to initialize integrated system")
        
        # Check for cancellation
        if progress_tracker.is_task_cancelled():
            raise Exception("Analysis cancelled by user")
        
        progress_tracker.update_step_status("initialization", TaskStatus.COMPLETED)
        
        # Step 2: Run integrated analysis
        progress_tracker.update_step_status("integrated_analysis", TaskStatus.RUNNING)
        session_manager.add_processing_log("Running integrated CV analysis")
        
        # Progress callback for integration manager
        def progress_callback(progress_data):
            if progress_tracker.is_task_cancelled():
                return
            
            # Update progress based on integration manager feedback
            if 'current_item' in progress_data:
                session_manager.add_processing_log(f"Processing: {progress_data['current_item']}")
        
        # Run the integrated analysis
        batch_result = await integration_mgr.run_integrated_analysis(
            file_data, job_description, config, progress_callback
        )
        
        progress_tracker.update_step_status("integrated_analysis", TaskStatus.COMPLETED)
        
        # Step 3: Process results
        progress_tracker.update_step_status("results_processing", TaskStatus.RUNNING)
        session_manager.add_processing_log("Processing analysis results")
        
        # Check for cancellation
        if progress_tracker.is_task_cancelled():
            raise Exception("Analysis cancelled by user")
        
        # Update session with results
        session_manager.update_session_results(session_id, batch_result)
        
        progress_tracker.update_step_status("results_processing", TaskStatus.COMPLETED)
        
        # Finish progress tracking
        progress_tracker.finish_tracking(TaskStatus.COMPLETED)
        
        # Log completion
        session_manager.add_processing_log(
            f"Integrated analysis completed for session {session_id} in {batch_result.processing_time_seconds:.1f}s"
        )
        
        # Update UI state with enhanced results
        success_msg = f"✅ Analysis completed! Processed {batch_result.total_processed} CVs"
        if batch_result.successful_count != batch_result.total_processed:
            success_msg += f" ({batch_result.successful_count} successful, {batch_result.failed_count} failed)"
        success_msg += f" in {batch_result.processing_time_seconds:.1f} seconds"
        
        st.success(success_msg)
        
        # Show system insights if available
        if hasattr(batch_result, 'batch_insights') and batch_result.batch_insights:
            insights = batch_result.batch_insights
            if 'recommendations' in insights and insights['recommendations']:
                st.info("💡 **Analysis Insights:**\n" + "\n".join(f"• {rec}" for rec in insights['recommendations']))
        
        # Show system red flags if any
        if hasattr(batch_result, 'system_red_flags') and batch_result.system_red_flags:
            st.warning("⚠️ **System Alerts:**\n" + "\n".join(f"• {flag.description}" for flag in batch_result.system_red_flags))
        
    except Exception as e:
        # Handle cancellation vs errors
        if "cancelled" in str(e).lower():
            progress_tracker.finish_tracking(TaskStatus.CANCELLED)
            session_manager.add_processing_log(f"Analysis cancelled for session {session_id}")
            st.warning("⚠️ Analysis was cancelled by user")
        else:
            progress_tracker.finish_tracking(TaskStatus.FAILED)
            session_manager.add_error(f"Analysis failed: {str(e)}", {
                'session_id': session_id,
                'file_count': len(file_data),
                'processing_time': (datetime.now() - start_time).total_seconds()
            })
            st.error(f"❌ Analysis failed: {str(e)}")
        
        # Re-raise for UI handling
        raise e
    
    finally:
        # Clean up progress tracker
        clear_current_tracker()


def load_session(session_id: str):
    """Load a specific session."""
    session = session_manager.get_session_by_id(session_id)
    if session:
        st.session_state.current_session_id = session_id
        st.session_state.current_analysis_results = session.results
        st.session_state.job_description = session.job_description
        st.session_state.analysis_config = session.config
        st.session_state.current_processing_status = session.status
        
        st.success(f"Loaded session from {session.timestamp.strftime('%m/%d/%Y %H:%M')}")
        st.rerun()


def render_progress_tracking():
    """Render real-time progress tracking interface."""
    from progress_tracker import get_current_tracker
    
    tracker = get_current_tracker()
    if not tracker:
        return
    
    # Progress tracking is handled automatically by the tracker's UI update system
    # This function serves as a placeholder for any additional progress UI elements
    
    # Add a status indicator in the sidebar
    with st.sidebar:
        st.markdown("### 🔄 Analysis Status")
        
        metrics = tracker.get_progress_metrics()
        
        # Overall progress indicator
        st.progress(metrics.overall_progress)
        st.write(f"Progress: {metrics.overall_progress:.1%}")
        
        # Current step indicator
        if metrics.current_step:
            st.info(f"Current: {metrics.current_step}")
        
        # Time information
        if metrics.estimated_time_remaining:
            remaining_str = str(metrics.estimated_time_remaining).split('.')[0]
            st.write(f"⏱️ Est. remaining: {remaining_str}")
        
        # Quick stats
        col1, col2 = st.columns(2)
        with col1:
            st.metric("Completed", f"{metrics.completed_steps}/{metrics.total_steps}")
        with col2:
            if metrics.failed_steps > 0:
                st.metric("Failed", metrics.failed_steps, delta_color="inverse")


def add_to_comparison(session_id: str):
    """Add a session to comparison mode."""
    if session_id not in st.session_state.selected_sessions_for_comparison:
        st.session_state.selected_sessions_for_comparison.append(session_id)
        
        if len(st.session_state.selected_sessions_for_comparison) >= 2:
            session_manager.enable_comparison_mode(st.session_state.selected_sessions_for_comparison)
            st.success("Comparison mode enabled!")
        else:
            st.info("Select another session to enable comparison mode")
        
        st.rerun()


def render_footer():
    """Render application footer with version and links."""
    st.markdown("---")
    col1, col2, col3 = st.columns(3)
    
    with col1:
        st.markdown("**CV Analyzer Enhancement v1.0**")
    
    with col2:
        st.markdown("Built with Streamlit & AI")
    
    with col3:
        st.markdown("🔒 Privacy-focused analysis")


def render_header():
    """Render the main application header."""
    st.markdown('<div class="main-header">📄 CV Analyzer Enhancement</div>', unsafe_allow_html=True)
    st.markdown("""
    <div style="text-align: center; color: #666; margin-bottom: 2rem;">
        Advanced AI-powered CV analysis tool for recruiters and hiring managers
    </div>
    """, unsafe_allow_html=True)


def render_upload_section() -> List[Dict[str, Any]]:
    """
    Render the file upload section and return uploaded file data.
    
    Returns:
        List of dictionaries containing file data with keys: 'name', 'content', 'size'
    """
    st.markdown('<div class="section-header">📁 Upload CV Files</div>', unsafe_allow_html=True)
    
    with st.container():
        st.markdown('<div class="upload-section">', unsafe_allow_html=True)
        
        # File uploader widget with enhanced validation
        uploaded_files = st.file_uploader(
            "Choose PDF files to analyze",
            type=['pdf'],
            accept_multiple_files=True,
            help="Upload one or more PDF files containing candidate CVs. Maximum file size: 10MB per file.",
            key="cv_uploader"
        )
        
        # Display upload instructions and requirements
        if not uploaded_files:
            st.markdown("""
            <div class="info-message">
                <strong>📋 Upload Requirements:</strong>
                <ul>
                    <li><strong>File Format:</strong> PDF files only (.pdf extension)</li>
                    <li><strong>File Size:</strong> Maximum 10MB per file</li>
                    <li><strong>Multiple Files:</strong> You can upload multiple CVs for batch analysis</li>
                    <li><strong>Content:</strong> Ensure PDFs contain readable text (not just images)</li>
                    <li><strong>Language:</strong> English language CVs are recommended for best results</li>
                </ul>
                <p><strong>💡 Tip:</strong> For best analysis results, ensure CVs include clear sections for work experience, skills, and education.</p>
            </div>
            """, unsafe_allow_html=True)
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    # Process and validate uploaded files
    file_data = []
    validation_errors = []
    
    if uploaded_files:
        # Validate file count
        if len(uploaded_files) > 20:
            validation_errors.append("⚠️ Maximum 20 files allowed per batch. Please reduce the number of files.")
        
        # Process each file with validation
        valid_files = 0
        total_size = 0
        
        for i, file in enumerate(uploaded_files):
            file_errors = []
            
            # Validate file size (10MB limit)
            if file.size > 10 * 1024 * 1024:
                file_errors.append(f"File size ({file.size / 1024 / 1024:.1f}MB) exceeds 10MB limit")
            
            # Validate file extension
            if not file.name.lower().endswith('.pdf'):
                file_errors.append("File must have .pdf extension")
            
            # Validate file name
            if len(file.name) > 100:
                file_errors.append("File name too long (max 100 characters)")
            
            # Check for empty files
            if file.size == 0:
                file_errors.append("File appears to be empty")
            
            total_size += file.size
            
            if file_errors:
                for error in file_errors:
                    validation_errors.append(f"❌ {file.name}: {error}")
            else:
                valid_files += 1
                try:
                    # Extract text from PDF using PyPDF2
                    pdf_reader = PdfReader(io.BytesIO(file.read()))
                    extracted_text = ""
                    for page in pdf_reader.pages:
                        extracted_text += page.extract_text() + "\n"

                    # Reset file pointer for potential future use
                    file.seek(0)

                    if not extracted_text.strip():
                        validation_errors.append(f"❌ {file.name}: No text could be extracted from PDF")
                        continue

                    file_data.append({
                        'name': file.name,
                        'content': extracted_text,
                        'size': file.size,
                        'type': 'pdf',
                        'upload_time': datetime.now().isoformat()
                    })
                except Exception as e:
                    validation_errors.append(f"❌ Error processing {file.name}: {str(e)}")
        
        # Display validation results
        if validation_errors:
            st.markdown('<div class="error-message">', unsafe_allow_html=True)
            st.error("⚠️ File Validation Issues:")
            for error in validation_errors:
                st.write(f"• {error}")
            st.markdown('</div>', unsafe_allow_html=True)
        
        # Display success summary for valid files
        if valid_files > 0:
            st.success(f"✅ {valid_files} file(s) uploaded successfully")
            
            # Show batch statistics
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("Valid Files", valid_files)
            with col2:
                st.metric("Total Size", f"{total_size / 1024 / 1024:.1f} MB")
            with col3:
                st.metric("Average Size", f"{(total_size / len(uploaded_files)) / 1024 / 1024:.1f} MB")
            with col4:
                st.metric("Invalid Files", len(uploaded_files) - valid_files)
            
            # Display detailed file information
            with st.expander("📋 Uploaded Files Details", expanded=False):
                for i, file in enumerate(uploaded_files):
                    col1, col2, col3, col4 = st.columns([3, 1, 1, 1])
                    with col1:
                        # Show file name with status indicator
                        is_valid = any(fd['name'] == file.name for fd in file_data)
                        status_icon = "✅" if is_valid else "❌"
                        st.write(f"{status_icon} **{file.name}**")
                    with col2:
                        st.write(f"{file.size / 1024:.1f} KB")
                    with col3:
                        st.write("PDF")
                    with col4:
                        st.write("Valid" if is_valid else "Invalid")
        
        # Show warning if no valid files
        elif uploaded_files:
            st.warning("⚠️ No valid files found. Please check the requirements and try again.")
    
    return file_data


def render_job_description_input() -> str:
    """
    Render the enhanced job description input section with validation and guidance.
    
    Returns:
        Job description text
    """
    st.markdown('<div class="section-header">💼 Job Description & Requirements</div>', unsafe_allow_html=True)
    
    with st.container():
        st.markdown('<div class="job-description-section">', unsafe_allow_html=True)
        
        # Add helpful guidance
        with st.expander("💡 Tips for Writing Effective Job Descriptions", expanded=False):
            st.markdown("""
            **For best analysis results, include:**
            - **Required Skills:** List mandatory technical skills and experience levels
            - **Preferred Skills:** Nice-to-have skills that would be beneficial
            - **Experience Level:** Years of experience required (e.g., "5+ years")
            - **Responsibilities:** Key duties and responsibilities of the role
            - **Industry Context:** Specific domain knowledge or industry experience
            - **Soft Skills:** Communication, leadership, teamwork requirements
            
            **Example Structure:**
            ```
            Position: Senior Python Developer
            
            Required Skills:
            - Python (5+ years)
            - Django/Flask frameworks
            - PostgreSQL database
            - REST API development
            
            Preferred Skills:
            - AWS/Cloud experience
            - Docker/Kubernetes
            - React/Frontend skills
            
            Responsibilities:
            - Design and develop web applications
            - Collaborate with cross-functional teams
            - Mentor junior developers
            ```
            """)
        
        # Job description text area with enhanced features
        job_description = st.text_area(
            "Enter the complete job description and requirements",
            value=st.session_state.job_description,
            height=250,
            placeholder="""Example Job Description:

Position: Senior Python Developer

We are seeking an experienced Python developer to join our growing engineering team.

REQUIRED QUALIFICATIONS:
• 5+ years of Python development experience
• Strong experience with Django or Flask frameworks
• Proficiency with PostgreSQL and database design
• Experience with REST API development and integration
• Knowledge of Git version control and collaborative development
• Bachelor's degree in Computer Science or equivalent experience

PREFERRED QUALIFICATIONS:
• AWS cloud services experience (EC2, S3, RDS)
• Docker and containerization experience
• Frontend development skills (React, JavaScript)
• Experience with CI/CD pipelines
• Agile/Scrum methodology experience
• Previous fintech or healthcare industry experience

RESPONSIBILITIES:
• Design and develop scalable web applications
• Collaborate with product managers and designers
• Write clean, maintainable, and well-tested code
• Participate in code reviews and technical discussions
• Mentor junior developers and contribute to team growth

COMPENSATION: $120,000 - $150,000 + benefits""",
            help="Provide detailed job requirements, responsibilities, required skills, and preferred qualifications. The more specific you are, the better the analysis will be.",
            key="job_description_input"
        )
        
        # Update session state
        st.session_state.job_description = job_description
        
        # Enhanced validation and feedback
        char_count = len(job_description.strip())
        word_count = len(job_description.strip().split()) if job_description.strip() else 0
        
        # Display statistics
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            st.metric("Characters", char_count)
        with col2:
            st.metric("Words", word_count)
        with col3:
            # Estimate reading time
            reading_time = max(1, word_count // 200)  # ~200 words per minute
            st.metric("Reading Time", f"{reading_time} min")
        with col4:
            # Quality score based on length and content
            quality_score = min(100, (char_count // 10) + (word_count // 2))
            st.metric("Quality Score", f"{quality_score}%")
        
        # Content analysis and suggestions
        if job_description.strip():
            job_desc_lower = job_description.lower()
            
            # Check for key sections
            has_requirements = any(keyword in job_desc_lower for keyword in ['required', 'must have', 'essential'])
            has_skills = any(keyword in job_desc_lower for keyword in ['skill', 'experience', 'knowledge', 'proficient'])
            has_responsibilities = any(keyword in job_desc_lower for keyword in ['responsibility', 'duties', 'role', 'will'])
            has_experience_level = any(keyword in job_desc_lower for keyword in ['years', 'experience', 'senior', 'junior'])
            
            # Display content analysis
            st.write("**Content Analysis:**")
            col1, col2 = st.columns(2)
            
            with col1:
                st.write(f"{'✅' if has_requirements else '❌'} Requirements section")
                st.write(f"{'✅' if has_skills else '❌'} Skills mentioned")
            
            with col2:
                st.write(f"{'✅' if has_responsibilities else '❌'} Responsibilities listed")
                st.write(f"{'✅' if has_experience_level else '❌'} Experience level specified")
            
            # Provide specific feedback
            suggestions = []
            if char_count < 200:
                suggestions.append("📝 Consider adding more detail - job descriptions under 200 characters may not provide enough context")
            elif char_count < 500:
                suggestions.append("📝 Good start! Consider adding more details about responsibilities and preferred qualifications")
            
            if not has_requirements:
                suggestions.append("🎯 Add a clear 'Required Skills' or 'Must Have' section")
            
            if not has_responsibilities:
                suggestions.append("📋 Include key responsibilities and daily tasks")
            
            if not has_experience_level:
                suggestions.append("⏱️ Specify required years of experience (e.g., '3+ years', 'Senior level')")
            
            # Count potential skills mentioned
            common_skills = ['python', 'java', 'javascript', 'react', 'angular', 'vue', 'node', 'django', 'flask', 'spring', 'docker', 'kubernetes', 'aws', 'azure', 'gcp', 'sql', 'postgresql', 'mysql', 'mongodb', 'git']
            mentioned_skills = [skill for skill in common_skills if skill in job_desc_lower]
            
            if mentioned_skills:
                st.write(f"**🔧 Technical Skills Detected:** {', '.join(mentioned_skills[:10])}")
                if len(mentioned_skills) > 10:
                    st.write(f"... and {len(mentioned_skills) - 10} more")
            
            # Display suggestions
            if suggestions:
                st.write("**💡 Suggestions for Improvement:**")
                for suggestion in suggestions:
                    st.write(f"• {suggestion}")
        
        else:
            st.info("💡 Start typing your job description above to see real-time analysis and suggestions")
        
        st.markdown('</div>', unsafe_allow_html=True)
    
    return job_description


def render_configuration_panel() -> AnalysisConfig:
    """
    Render the expandable configuration panel with analysis parameters.
    
    Returns:
        AnalysisConfig object with user-specified parameters
    """
    st.markdown('<div class="section-header">⚙️ Analysis Configuration</div>', unsafe_allow_html=True)
    
    with st.expander("🔧 Advanced Configuration Settings", expanded=False):
        st.markdown("""
        <div style="background-color: #f8f9fa; padding: 1rem; border-radius: 5px; margin-bottom: 1rem;">
        <strong>💡 Configuration Help:</strong><br>
        Customize these parameters to fine-tune the analysis based on your specific hiring criteria and industry standards.
        Default values work well for most positions, but you can adjust them for specialized roles.
        </div>
        """, unsafe_allow_html=True)
        
        # Create tabs for different configuration sections
        config_tab1, config_tab2, config_tab3, config_tab4 = st.tabs([
            "🎯 Skills & Requirements", 
            "📊 Career Analysis", 
            "🔍 Detection Thresholds", 
            "🤖 AI Settings"
        ])
        
        # Tab 1: Skills and Requirements
        with config_tab1:
            st.markdown("### Required vs Preferred Skills")
            st.markdown("Specify which skills are mandatory versus nice-to-have for better candidate matching.")
            
            col1, col2 = st.columns(2)
            
            with col1:
                st.markdown("**🎯 Mandatory Skills**")
                st.markdown("*These skills are required and will heavily impact the match score*")
                
                # Initialize mandatory skills if not in session state
                if 'mandatory_skills_text' not in st.session_state:
                    st.session_state.mandatory_skills_text = "\n".join(st.session_state.analysis_config.mandatory_skills)
                
                mandatory_skills_text = st.text_area(
                    "Enter mandatory skills (one per line)",
                    value=st.session_state.mandatory_skills_text,
                    height=150,
                    placeholder="""Python
Django/Flask
PostgreSQL
REST API
Git
5+ years experience""",
                    help="List each required skill on a separate line. Be specific about experience levels.",
                    key="mandatory_skills_input"
                )
                st.session_state.mandatory_skills_text = mandatory_skills_text
                
                # Parse mandatory skills
                mandatory_skills = [skill.strip() for skill in mandatory_skills_text.split('\n') if skill.strip()]
                
                if mandatory_skills:
                    st.success(f"✅ {len(mandatory_skills)} mandatory skills defined")
                else:
                    st.info("💡 Add mandatory skills to improve matching accuracy")
            
            with col2:
                st.markdown("**⭐ Preferred Skills**")
                st.markdown("*These skills are beneficial but not required*")
                
                # Initialize preferred skills if not in session state
                if 'preferred_skills_text' not in st.session_state:
                    st.session_state.preferred_skills_text = "\n".join(st.session_state.analysis_config.preferred_skills)
                
                preferred_skills_text = st.text_area(
                    "Enter preferred skills (one per line)",
                    value=st.session_state.preferred_skills_text,
                    height=150,
                    placeholder="""AWS/Cloud
Docker
React
CI/CD
Agile/Scrum
Team leadership""",
                    help="List each preferred skill on a separate line. These add bonus points but aren't required.",
                    key="preferred_skills_input"
                )
                st.session_state.preferred_skills_text = preferred_skills_text
                
                # Parse preferred skills
                preferred_skills = [skill.strip() for skill in preferred_skills_text.split('\n') if skill.strip()]
                
                if preferred_skills:
                    st.success(f"✅ {len(preferred_skills)} preferred skills defined")
                else:
                    st.info("💡 Add preferred skills for comprehensive evaluation")
            
            # Skill Context Weights
            st.markdown("### 🏷️ Skill Context Weighting")
            st.markdown("Adjust how different skill contexts are weighted in the analysis.")
            
            col1, col2, col3, col4 = st.columns(4)
            
            with col1:
                professional_weight = st.slider(
                    "Professional Experience",
                    min_value=0.0,
                    max_value=2.0,
                    value=st.session_state.analysis_config.skill_weights.get("professional", 1.0),
                    step=0.1,
                    help="Weight for skills from professional work experience"
                )
            
            with col2:
                academic_weight = st.slider(
                    "Academic/Educational",
                    min_value=0.0,
                    max_value=2.0,
                    value=st.session_state.analysis_config.skill_weights.get("academic", 0.3),
                    step=0.1,
                    help="Weight for skills from academic projects or coursework"
                )
            
            with col3:
                certification_weight = st.slider(
                    "Certifications",
                    min_value=0.0,
                    max_value=2.0,
                    value=st.session_state.analysis_config.skill_weights.get("certification", 0.8),
                    step=0.1,
                    help="Weight for skills from professional certifications"
                )
            
            with col4:
                personal_weight = st.slider(
                    "Personal Projects",
                    min_value=0.0,
                    max_value=2.0,
                    value=st.session_state.analysis_config.skill_weights.get("personal_project", 0.5),
                    step=0.1,
                    help="Weight for skills from personal or side projects"
                )
        
        # Tab 2: Career Analysis Settings
        with config_tab2:
            st.markdown("### 📈 Experience Analysis Parameters")
            
            col1, col2 = st.columns(2)
            
            with col1:
                recent_experience_years = st.slider(
                    "Recent Experience Window (years)",
                    min_value=1,
                    max_value=15,
                    value=st.session_state.analysis_config.recent_experience_years,
                    step=1,
                    help="How many years back to consider as 'recent' experience for skill relevance"
                )
                
                st.markdown("**💡 Recommendation:**")
                if recent_experience_years <= 3:
                    st.info("Short window - focuses on very current skills")
                elif recent_experience_years <= 7:
                    st.success("Balanced window - good for most positions")
                else:
                    st.warning("Long window - includes older experience")
            
            with col2:
                job_hopping_threshold = st.slider(
                    "Job Hopping Threshold",
                    min_value=2,
                    max_value=10,
                    value=st.session_state.analysis_config.job_hopping_threshold,
                    step=1,
                    help="Number of short positions that triggers a job hopping flag"
                )
                
                st.markdown("**💡 Industry Guidelines:**")
                if job_hopping_threshold <= 3:
                    st.warning("Strict - flags frequent job changes")
                elif job_hopping_threshold <= 5:
                    st.success("Balanced - standard for most industries")
                else:
                    st.info("Lenient - allows more job mobility")
        
        # Tab 3: Detection Thresholds
        with config_tab3:
            st.markdown("### 🚨 Red Flag Detection Settings")
            
            col1, col2 = st.columns(2)
            
            with col1:
                gap_threshold_months = st.slider(
                    "Employment Gap Threshold (months)",
                    min_value=1,
                    max_value=12,
                    value=st.session_state.analysis_config.gap_threshold_months,
                    step=1,
                    help="Minimum gap duration to flag as concerning"
                )
                
                st.markdown("**Gap Severity Levels:**")
                st.write(f"• **Minor:** {gap_threshold_months}-6 months")
                st.write(f"• **Moderate:** 6-12 months")
                st.write(f"• **Significant:** 12-24 months")
                st.write(f"• **Major:** 24+ months")
            
            with col2:
                short_position_threshold = st.slider(
                    "Short Position Threshold (months)",
                    min_value=1,
                    max_value=24,
                    value=st.session_state.analysis_config.short_position_threshold_months,
                    step=1,
                    help="Maximum duration to consider a position as 'short-term'"
                )
                
                st.markdown("**💡 Industry Context:**")
                if short_position_threshold <= 6:
                    st.warning("Very strict - flags positions under 6 months")
                elif short_position_threshold <= 12:
                    st.success("Standard - flags positions under 1 year")
                else:
                    st.info("Lenient - allows shorter positions")
        
        # Tab 4: AI Settings
        with config_tab4:
            st.markdown("### 🤖 AI Analysis Parameters")
            
            col1, col2 = st.columns(2)
            
            with col1:
                llm_temperature = st.slider(
                    "AI Creativity Level",
                    min_value=0.0,
                    max_value=2.0,
                    value=st.session_state.analysis_config.llm_temperature,
                    step=0.1,
                    help="Higher values make AI more creative but less consistent"
                )
                
                if llm_temperature <= 0.3:
                    st.info("🎯 Very focused - consistent but rigid")
                elif llm_temperature <= 0.7:
                    st.success("⚖️ Balanced - good mix of consistency and flexibility")
                elif llm_temperature <= 1.2:
                    st.warning("🎨 Creative - more varied but less predictable")
                else:
                    st.error("🌪️ Very creative - may produce inconsistent results")
            
            with col2:
                max_retries = st.slider(
                    "Maximum Retry Attempts",
                    min_value=1,
                    max_value=10,
                    value=st.session_state.analysis_config.max_retries,
                    step=1,
                    help="How many times to retry failed API calls"
                )
                
                timeout_seconds = st.slider(
                    "Analysis Timeout (seconds)",
                    min_value=30,
                    max_value=1800,
                    value=st.session_state.analysis_config.timeout_seconds,
                    step=30,
                    help="Maximum time to wait for analysis completion"
                )
        
        # Configuration Summary
        st.markdown("---")
        st.markdown("### 📋 Configuration Summary")
        
        col1, col2, col3 = st.columns(3)
        
        with col1:
            st.metric("Mandatory Skills", len(mandatory_skills))
            st.metric("Gap Threshold", f"{gap_threshold_months} months")
        
        with col2:
            st.metric("Preferred Skills", len(preferred_skills))
            st.metric("Short Position", f"{short_position_threshold} months")
        
        with col3:
            st.metric("Recent Experience", f"{recent_experience_years} years")
            st.metric("Job Hopping", f"{job_hopping_threshold} positions")
        
        # Reset to defaults button
        if st.button("🔄 Reset to Default Settings", help="Reset all configuration to default values"):
            st.session_state.analysis_config = AnalysisConfig()
            st.session_state.mandatory_skills_text = ""
            st.session_state.preferred_skills_text = ""
            st.success("✅ Configuration reset to defaults")
            st.rerun()
    
    # Create and return the updated configuration
    skill_weights = {
        "professional": professional_weight,
        "academic": academic_weight,
        "certification": certification_weight,
        "personal_project": personal_weight
    }
    
    config = AnalysisConfig(
        gap_threshold_months=gap_threshold_months,
        short_position_threshold_months=short_position_threshold,
        job_hopping_threshold=job_hopping_threshold,
        recent_experience_years=recent_experience_years,
        skill_weights=skill_weights,
        mandatory_skills=mandatory_skills,
        preferred_skills=preferred_skills,
        llm_temperature=llm_temperature,
        max_retries=max_retries,
        timeout_seconds=timeout_seconds
    )
    
    # Update session state
    st.session_state.analysis_config = config
    
    return config


def validate_inputs(file_data: List[Dict[str, Any]], job_description: str) -> tuple[bool, List[str]]:
    """
    Validate user inputs before analysis with comprehensive checks.
    
    Args:
        file_data: List of uploaded file data
        job_description: Job description text
        
    Returns:
        Tuple of (is_valid, error_messages)
    """
    errors = []
    warnings = []
    
    # Validate file uploads
    if not file_data:
        errors.append("📁 Please upload at least one valid CV file")
    else:
        # Check individual file constraints
        for file in file_data:
            # File size validation (already done in upload, but double-check)
            if file['size'] > 10 * 1024 * 1024:  # 10MB limit
                errors.append(f"📁 File '{file['name']}' exceeds 10MB size limit")
            
            # Check for very small files (likely empty or corrupted)
            if file['size'] < 1024:  # Less than 1KB
                warnings.append(f"📁 File '{file['name']}' is very small ({file['size']} bytes) - may not contain sufficient content")
        
        # Check batch size limits
        if len(file_data) > 20:
            errors.append("📁 Maximum 20 files allowed per batch analysis")
        
        # Check total batch size
        total_size = sum(file['size'] for file in file_data)
        if total_size > 100 * 1024 * 1024:  # 100MB total limit
            errors.append(f"📁 Total batch size ({total_size / 1024 / 1024:.1f}MB) exceeds 100MB limit")
    
    # Validate job description
    job_desc_stripped = job_description.strip()
    if not job_desc_stripped:
        errors.append("💼 Please enter a job description")
    else:
        # Check minimum length
        if len(job_desc_stripped) < 50:
            errors.append("💼 Job description should be at least 50 characters long for meaningful analysis")
        
        # Check maximum length
        if len(job_desc_stripped) > 10000:
            errors.append("💼 Job description is too long (max 10,000 characters)")
        
        # Check for basic content requirements
        job_desc_lower = job_desc_stripped.lower()
        
        # Warn if no skills mentioned
        skill_keywords = ['skill', 'experience', 'knowledge', 'proficient', 'familiar', 'expertise']
        if not any(keyword in job_desc_lower for keyword in skill_keywords):
            warnings.append("💼 Job description doesn't mention specific skills or requirements - this may affect analysis quality")
        
        # Warn if very short
        if len(job_desc_stripped) < 200:
            warnings.append("💼 Job description is quite brief - consider adding more details about requirements, responsibilities, and preferred qualifications")
    
    # Check for API key and environment setup
    api_key = os.getenv('GROQ_API_KEY')
    if not api_key:
        errors.append("🔑 GROQ_API_KEY environment variable is not set. Please configure your API key.")
    elif len(api_key.strip()) < 10:
        errors.append("🔑 GROQ_API_KEY appears to be invalid (too short)")
    
    # Check for required environment variables
    required_env_vars = ['GROQ_API_KEY']
    for var in required_env_vars:
        if not os.getenv(var):
            errors.append(f"🔑 Required environment variable '{var}' is not set")
    
    # System resource checks
    try:
        import psutil
        # Check available memory (warn if less than 1GB)
        available_memory = psutil.virtual_memory().available
        if available_memory < 1024 * 1024 * 1024:  # 1GB
            warnings.append(f"💻 Low available memory ({available_memory / 1024 / 1024:.0f}MB) - analysis may be slower")
    except ImportError:
        # psutil not available, skip memory check
        pass
    
    # Display warnings if any (but don't block processing)
    if warnings:
        st.warning("⚠️ **Warnings (analysis can still proceed):**")
        for warning in warnings:
            st.write(f"• {warning}")
    
    return len(errors) == 0, errors


def render_validation_messages(is_valid: bool, errors: List[str]):
    """Render comprehensive validation error messages with helpful guidance."""
    if not is_valid:
        st.markdown('<div class="error-message">', unsafe_allow_html=True)
        st.error("❌ **Please fix the following issues before proceeding:**")
        
        # Group errors by category for better organization
        file_errors = [e for e in errors if e.startswith('📁')]
        job_errors = [e for e in errors if e.startswith('💼')]
        api_errors = [e for e in errors if e.startswith('🔑')]
        system_errors = [e for e in errors if e.startswith('💻')]
        other_errors = [e for e in errors if not any(e.startswith(prefix) for prefix in ['📁', '💼', '🔑', '💻'])]
        
        # Display file-related errors
        if file_errors:
            st.write("**File Upload Issues:**")
            for error in file_errors:
                st.write(f"• {error}")
            st.write("")
        
        # Display job description errors
        if job_errors:
            st.write("**Job Description Issues:**")
            for error in job_errors:
                st.write(f"• {error}")
            st.write("")
        
        # Display API/configuration errors
        if api_errors:
            st.write("**Configuration Issues:**")
            for error in api_errors:
                st.write(f"• {error}")
            st.write("💡 **How to fix:** Set your GROQ API key as an environment variable or in your .env file")
            st.write("")
        
        # Display system errors
        if system_errors:
            st.write("**System Issues:**")
            for error in system_errors:
                st.write(f"• {error}")
            st.write("")
        
        # Display other errors
        if other_errors:
            st.write("**Other Issues:**")
            for error in other_errors:
                st.write(f"• {error}")
        
        # Provide helpful guidance
        st.markdown("""
        **🔧 Quick Fixes:**
        - **File Issues:** Ensure PDF files are under 10MB and contain readable text
        - **Job Description:** Add more details about required skills, experience, and responsibilities  
        - **API Key:** Contact your administrator or check the setup documentation
        - **Need Help?** Check the documentation or contact support
        """)
        
        st.markdown('</div>', unsafe_allow_html=True)


def render_analysis_button(is_valid: bool, file_data: List[Dict[str, Any]], job_description: str, analysis_config: AnalysisConfig) -> bool:
    """
    Render the enhanced analysis button with pre-analysis summary and confirmation.
    
    Args:
        is_valid: Whether inputs are valid
        file_data: List of uploaded file data
        job_description: Job description text
        analysis_config: Analysis configuration from the configuration panel
    
    Returns:
        True if analysis should be started
    """
    st.markdown('<div class="section-header">🚀 Start Analysis</div>', unsafe_allow_html=True)
    
    # Pre-analysis summary
    if file_data and job_description.strip():
        with st.container():
            st.markdown("**📊 Analysis Summary:**")
            
            col1, col2, col3, col4 = st.columns(4)
            with col1:
                st.metric("CVs to Analyze", len(file_data))
            with col2:
                total_size_mb = sum(f['size'] for f in file_data) / 1024 / 1024
                st.metric("Total Size", f"{total_size_mb:.1f} MB")
            with col3:
                # Estimate processing time (rough estimate: 30-60 seconds per CV)
                estimated_time = len(file_data) * 45  # seconds
                if estimated_time < 60:
                    time_str = f"{estimated_time}s"
                else:
                    time_str = f"{estimated_time // 60}m {estimated_time % 60}s"
                st.metric("Est. Time", time_str)
            with col4:
                job_desc_words = len(job_description.strip().split())
                st.metric("Job Desc Words", job_desc_words)
            
            # Analysis configuration preview
            with st.expander("🔧 Analysis Configuration", expanded=False):
                col1, col2 = st.columns(2)
                
                with col1:
                    st.write("**Detection Thresholds:**")
                    st.write(f"• Employment gap: {analysis_config.gap_threshold_months} months")
                    st.write(f"• Short position: {analysis_config.short_position_threshold_months} months")
                    st.write(f"• Job hopping: {analysis_config.job_hopping_threshold} positions")
                    
                    st.write("**Skills Configuration:**")
                    st.write(f"• Mandatory skills: {len(analysis_config.mandatory_skills)}")
                    st.write(f"• Preferred skills: {len(analysis_config.preferred_skills)}")
                
                with col2:
                    st.write("**Analysis Scope:**")
                    st.write(f"• Recent experience: {analysis_config.recent_experience_years} years")
                    st.write(f"• LLM temperature: {analysis_config.llm_temperature}")
                    st.write(f"• Max retries: {analysis_config.max_retries}")
                    
                    st.write("**Skill Weights:**")
                    st.write(f"• Professional: {analysis_config.skill_weights.get('professional', 1.0):.1f}")
                    st.write(f"• Academic: {analysis_config.skill_weights.get('academic', 0.3):.1f}")
                    st.write(f"• Certification: {analysis_config.skill_weights.get('certification', 0.8):.1f}")
    
    # Analysis button with enhanced styling
    col1, col2, col3 = st.columns([1, 2, 1])
    with col2:
        # Show different button states based on validation
        if not is_valid:
            st.button(
                "❌ Fix Issues First",
                disabled=True,
                use_container_width=True,
                help="Please resolve the validation errors above before starting analysis"
            )
        elif not file_data:
            st.button(
                "📁 Upload CVs First",
                disabled=True,
                use_container_width=True,
                help="Please upload at least one CV file"
            )
        elif not job_description.strip():
            st.button(
                "💼 Add Job Description",
                disabled=True,
                use_container_width=True,
                help="Please enter a job description"
            )
        else:
            # Ready to analyze
            if st.button(
                f"🔍 Analyze {len(file_data)} CV{'s' if len(file_data) > 1 else ''}",
                use_container_width=True,
                type="primary",
                help=f"Start analyzing {len(file_data)} CV(s) against the job requirements"
            ):
                return True
    
    # Display readiness status
    if is_valid and file_data and job_description.strip():
        st.success(f"✅ Ready to analyze {len(file_data)} CV(s) against the provided job description")
        
        # Show what will be analyzed
        with st.expander("📋 Files to be Analyzed", expanded=False):
            for i, file in enumerate(file_data, 1):
                col1, col2, col3 = st.columns([3, 1, 1])
                with col1:
                    st.write(f"{i}. **{file['name']}**")
                with col2:
                    st.write(f"{file['size'] / 1024:.1f} KB")
                with col3:
                    st.write("✅ Ready")
        
        # Analysis disclaimer
        st.info("""
        **📝 Analysis Disclaimer:**
        • Results are AI-generated and should be used as a screening tool, not the sole basis for hiring decisions
        • Manual review of promising candidates is always recommended
        • Analysis quality depends on CV format and job description clarity
        • Processing may take several minutes for large batches
        """)
    
    elif file_data or job_description.strip():
        # Partial readiness
        missing_items = []
        if not file_data:
            missing_items.append("CV files")
        if not job_description.strip():
            missing_items.append("job description")
        
        st.warning(f"⚠️ Please add: {', '.join(missing_items)}")
    
    return False


def render_processing_status():
    """Render processing status and progress."""
    if st.session_state.processing_status:
        st.markdown('<div class="section-header">⏳ Processing Status</div>', unsafe_allow_html=True)
        
        status = st.session_state.processing_status
        
        # Progress bar
        if 'progress' in status and status['progress']:
            progress_data = status['progress']
            progress_percentage = progress_data.get('progress_percentage', 0)
            
            st.progress(progress_percentage / 100)
            
            col1, col2, col3 = st.columns(3)
            with col1:
                st.metric("Completed", progress_data.get('completed_items', 0))
            with col2:
                st.metric("Failed", progress_data.get('failed_items', 0))
            with col3:
                st.metric("Total", progress_data.get('total_items', 0))
            
            if progress_data.get('current_item'):
                st.write(f"Currently processing: **{progress_data['current_item']}**")
            
            # Estimated time remaining
            if progress_data.get('estimated_remaining_seconds', 0) > 0:
                remaining_time = progress_data['estimated_remaining_seconds']
                st.write(f"⏱️ Estimated time remaining: {remaining_time:.0f} seconds")


async def run_analysis(file_data: List[Dict[str, Any]], job_description: str, config: AnalysisConfig):
    """
    Run the CV analysis asynchronously.
    
    Args:
        file_data: List of uploaded file data
        job_description: Job description text
        config: Analysis configuration
    """
    try:
        # Initialize analysis engine
        api_key = os.getenv('GROQ_API_KEY')
        engine = AnalysisEngine(config, api_key)
        
        # Progress callback to update UI
        def progress_callback(progress_data):
            st.session_state.processing_status = {'progress': progress_data}
        
        # Run analysis
        st.session_state.processing_status = {'is_processing': True}
        batch_result = await engine.analyze_candidates(
            file_data, 
            job_description, 
            progress_callback
        )
        
        # Store results
        st.session_state.analysis_results = batch_result
        st.session_state.processing_status = None
        
        # Show completion message
        st.success(f"✅ Analysis completed! Processed {batch_result.total_processed} CVs in {batch_result.processing_time_seconds:.1f} seconds")
        
    except Exception as e:
        st.error(f"❌ Analysis failed: {str(e)}")
        st.session_state.processing_status = None
        logger.error(f"Analysis error: {e}")


def render_results_dashboard(batch_result):
    """
    Render comprehensive results dashboard with sortable tables, charts, and detailed views.
    
    Args:
        batch_result: BatchProcessingResult containing all analysis results
    """
    import pandas as pd
    import plotly.express as px
    import plotly.graph_objects as go
    from plotly.subplots import make_subplots
    
    st.markdown('<div class="section-header">📊 Analysis Results Dashboard</div>', unsafe_allow_html=True)
    
    # Results summary with metrics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Processed", batch_result.total_processed)
    with col2:
        st.metric("Successful", batch_result.successful_count, 
                 delta=f"{(batch_result.successful_count/batch_result.total_processed*100):.1f}%" if batch_result.total_processed > 0 else "0%")
    with col3:
        st.metric("Failed", batch_result.failed_count,
                 delta=f"{(batch_result.failed_count/batch_result.total_processed*100):.1f}%" if batch_result.total_processed > 0 else "0%")
    with col4:
        st.metric("Processing Time", f"{batch_result.processing_time_seconds:.1f}s")
    
    if not batch_result.assessments:
        st.warning("⚠️ No assessments found to display.")
        return
    
    # Filter successful assessments
    successful_assessments = [a for a in batch_result.assessments 
                            if a.processing_status == ProcessingStatus.COMPLETED]
    
    if not successful_assessments:
        st.error("❌ No successful analyses found.")
        # Show errors if any
        if batch_result.errors:
            with st.expander("❌ Processing Errors", expanded=True):
                for error in batch_result.errors:
                    st.error(error)
        return
    
    # Dashboard tabs
    tab1, tab2, tab3, tab4 = st.tabs([
        "📋 Candidates Overview", 
        "📊 Analytics & Charts", 
        "👤 Individual Details", 
        "🚩 Red Flags Analysis"
    ])
    
    # Tab 1: Candidates Overview - Sortable comparison table
    with tab1:
        st.markdown("### 📋 Candidates Comparison Table")
        
        # Create comparison dataframe
        comparison_data = []
        for assessment in successful_assessments:
            # Calculate summary metrics
            red_flags_count = len(assessment.red_flags)
            critical_flags = len([f for f in assessment.red_flags if f.severity.value == 'critical'])
            high_flags = len([f for f in assessment.red_flags if f.severity.value == 'high'])
            
            # Get skill counts
            professional_skills = len(assessment.skill_analysis.professional_skills) if assessment.skill_analysis else 0
            recent_skills = len(assessment.skill_analysis.recent_skills) if assessment.skill_analysis else 0
            
            # Get stability score
            stability_score = assessment.job_stability_analysis.stability_score if assessment.job_stability_analysis else 0.5
            
            comparison_data.append({
                'Candidate': assessment.candidate_name or 'Unknown',
                'Match Score': f"{assessment.match_score:.1%}",
                'Match Score (Numeric)': assessment.match_score,
                'Experience (Years)': assessment.experience_years,
                'Current Position': assessment.current_position or 'Not specified',
                'Professional Skills': professional_skills,
                'Recent Skills': recent_skills,
                'Stability Score': f"{stability_score:.2f}",
                'Stability Score (Numeric)': stability_score,
                'Total Red Flags': red_flags_count,
                'Critical Flags': critical_flags,
                'High Flags': high_flags,
                'Email': assessment.email or 'Not provided',
                'Phone': assessment.contact_phone or 'Not provided',
                'Processing Time': f"{assessment.processing_time_seconds:.1f}s" if assessment.processing_time_seconds else 'N/A'
            })
        
        df = pd.DataFrame(comparison_data)
        
        # Sorting options
        col1, col2, col3 = st.columns([2, 2, 1])
        with col1:
            sort_by = st.selectbox(
                "Sort by:",
                options=['Match Score (Numeric)', 'Experience (Years)', 'Stability Score (Numeric)', 
                        'Total Red Flags', 'Professional Skills', 'Recent Skills'],
                index=0,
                key="sort_by_select"
            )
        with col2:
            sort_order = st.selectbox(
                "Order:",
                options=['Descending', 'Ascending'],
                index=0,
                key="sort_order_select"
            )
        with col3:
            show_all_columns = st.checkbox("Show all columns", value=False)
        
        # Sort dataframe
        ascending = sort_order == 'Ascending'
        df_sorted = df.sort_values(by=sort_by, ascending=ascending)
        
        # Select columns to display
        if show_all_columns:
            display_columns = df.columns.tolist()
        else:
            display_columns = ['Candidate', 'Match Score', 'Experience (Years)', 
                             'Current Position', 'Professional Skills', 'Total Red Flags', 
                             'Critical Flags', 'Stability Score']
        
        # Display the table with styling
        st.dataframe(
            df_sorted[display_columns],
            use_container_width=True,
            hide_index=True,
            column_config={
                'Match Score': st.column_config.ProgressColumn(
                    'Match Score',
                    help='Overall match percentage',
                    min_value=0,
                    max_value=1,
                    format="%.1%"
                ),
                'Stability Score': st.column_config.ProgressColumn(
                    'Stability Score',
                    help='Job stability score (higher is better)',
                    min_value=0,
                    max_value=1,
                    format="%.2f"
                ),
                'Total Red Flags': st.column_config.NumberColumn(
                    'Total Red Flags',
                    help='Total number of red flags identified',
                    format="%d"
                ),
                'Critical Flags': st.column_config.NumberColumn(
                    'Critical Flags',
                    help='Number of critical red flags',
                    format="%d"
                )
            }
        )
        
        # Quick stats
        st.markdown("### 📈 Quick Statistics")
        col1, col2, col3, col4 = st.columns(4)
        with col1:
            avg_match = df['Match Score (Numeric)'].mean()
            st.metric("Average Match Score", f"{avg_match:.1%}")
        with col2:
            avg_experience = df['Experience (Years)'].mean()
            st.metric("Average Experience", f"{avg_experience:.1f} years")
        with col3:
            total_red_flags = df['Total Red Flags'].sum()
            st.metric("Total Red Flags", total_red_flags)
        with col4:
            candidates_with_critical = len(df[df['Critical Flags'] > 0])
            st.metric("Candidates with Critical Issues", candidates_with_critical)
    
    # Tab 2: Analytics & Charts
    with tab2:
        st.markdown("### 📊 Interactive Analytics")
        
        # Match Score Distribution
        st.markdown("#### Match Score Distribution")
        match_scores = [a.match_score for a in successful_assessments]
        
        fig_hist = px.histogram(
            x=match_scores,
            nbins=10,
            title="Distribution of Match Scores",
            labels={'x': 'Match Score', 'y': 'Number of Candidates'},
            color_discrete_sequence=['#1f77b4']
        )
        fig_hist.update_layout(showlegend=False)
        st.plotly_chart(fig_hist, use_container_width=True)
        
        # Experience vs Match Score Scatter Plot
        st.markdown("#### Experience vs Match Score")
        experience_data = []
        for a in successful_assessments:
            red_flags_count = len(a.red_flags)
            experience_data.append({
                'Candidate': a.candidate_name or 'Unknown',
                'Experience (Years)': a.experience_years,
                'Match Score': a.match_score,
                'Red Flags': red_flags_count,
                'Size': max(5, 20 - red_flags_count)  # Larger bubbles for fewer red flags
            })
        
        exp_df = pd.DataFrame(experience_data)
        fig_scatter = px.scatter(
            exp_df,
            x='Experience (Years)',
            y='Match Score',
            size='Size',
            hover_name='Candidate',
            hover_data=['Red Flags'],
            title="Experience vs Match Score (Bubble size inversely related to red flags)",
            labels={'Match Score': 'Match Score (%)', 'Experience (Years)': 'Years of Experience'}
        )
        fig_scatter.update_traces(marker=dict(opacity=0.7))
        st.plotly_chart(fig_scatter, use_container_width=True)
        
        # Red Flags Analysis
        st.markdown("#### Red Flags Analysis")
        
        # Collect red flag data
        red_flag_data = {}
        severity_data = {'low': 0, 'medium': 0, 'high': 0, 'critical': 0}
        
        for assessment in successful_assessments:
            for flag in assessment.red_flags:
                flag_type = flag.type.value.replace('_', ' ').title()
                red_flag_data[flag_type] = red_flag_data.get(flag_type, 0) + 1
                severity_data[flag.severity.value] += 1
        
        if red_flag_data:
            col1, col2 = st.columns(2)
            
            with col1:
                # Red flag types chart
                fig_flags = px.bar(
                    x=list(red_flag_data.keys()),
                    y=list(red_flag_data.values()),
                    title="Red Flag Types Distribution",
                    labels={'x': 'Red Flag Type', 'y': 'Count'},
                    color=list(red_flag_data.values()),
                    color_continuous_scale='Reds'
                )
                fig_flags.update_layout(showlegend=False, xaxis_tickangle=-45)
                st.plotly_chart(fig_flags, use_container_width=True)
            
            with col2:
                # Severity distribution pie chart
                fig_severity = px.pie(
                    values=list(severity_data.values()),
                    names=[s.title() for s in severity_data.keys()],
                    title="Red Flag Severity Distribution",
                    color_discrete_map={
                        'Low': '#90EE90',
                        'Medium': '#FFD700', 
                        'High': '#FFA500',
                        'Critical': '#FF4500'
                    }
                )
                st.plotly_chart(fig_severity, use_container_width=True)
        else:
            st.info("🎉 No red flags found across all candidates!")
        
        # Skills Analysis
        st.markdown("#### Skills Analysis")
        
        # Collect skill data
        all_professional_skills = []
        all_recent_skills = []
        
        for assessment in successful_assessments:
            if assessment.skill_analysis:
                all_professional_skills.extend(assessment.skill_analysis.professional_skills)
                all_recent_skills.extend(assessment.skill_analysis.recent_skills)
        
        if all_professional_skills:
            # Most common professional skills
            from collections import Counter
            skill_counts = Counter(all_professional_skills)
            top_skills = dict(skill_counts.most_common(10))
            
            fig_skills = px.bar(
                x=list(top_skills.values()),
                y=list(top_skills.keys()),
                orientation='h',
                title="Top 10 Professional Skills Across All Candidates",
                labels={'x': 'Number of Candidates', 'y': 'Skills'},
                color=list(top_skills.values()),
                color_continuous_scale='Blues'
            )
            fig_skills.update_layout(showlegend=False)
            st.plotly_chart(fig_skills, use_container_width=True)
        
        # Recent skills analysis
        if all_recent_skills:
            recent_skill_counts = Counter(all_recent_skills)
            top_recent_skills = dict(recent_skill_counts.most_common(8))
            
            fig_recent = px.bar(
                x=list(top_recent_skills.keys()),
                y=list(top_recent_skills.values()),
                title="Most Common Recent Skills",
                labels={'x': 'Skills', 'y': 'Number of Candidates'},
                color=list(top_recent_skills.values()),
                color_continuous_scale='Greens'
            )
            fig_recent.update_layout(showlegend=False, xaxis_tickangle=-45)
            st.plotly_chart(fig_recent, use_container_width=True)
    
    # Tab 3: Individual Details
    with tab3:
        st.markdown("### 👤 Individual Candidate Details")
        
        # Candidate selection
        candidate_names = [a.candidate_name or f'Candidate {i+1}' for i, a in enumerate(successful_assessments)]
        selected_candidate = st.selectbox(
            "Select a candidate to view detailed analysis:",
            options=candidate_names,
            key="candidate_detail_select"
        )
        
        # Find selected assessment
        selected_assessment = None
        for i, assessment in enumerate(successful_assessments):
            if (assessment.candidate_name or f'Candidate {i+1}') == selected_candidate:
                selected_assessment = assessment
                break
        
        if selected_assessment:
            # Candidate header
            col1, col2, col3 = st.columns([2, 1, 1])
            with col1:
                st.markdown(f"## {selected_assessment.candidate_name or 'Unknown Candidate'}")
                st.markdown(f"**Current Position:** {selected_assessment.current_position or 'Not specified'}")
            with col2:
                st.metric("Match Score", f"{selected_assessment.match_score:.1%}")
            with col3:
                st.metric("Experience", f"{selected_assessment.experience_years} years")
            
            # Contact information
            if selected_assessment.email or selected_assessment.contact_phone:
                st.markdown("### 📞 Contact Information")
                contact_col1, contact_col2 = st.columns(2)
                with contact_col1:
                    if selected_assessment.email:
                        st.write(f"**Email:** {selected_assessment.email}")
                    if selected_assessment.linkedin:
                        st.write(f"**LinkedIn:** {selected_assessment.linkedin}")
                with contact_col2:
                    if selected_assessment.contact_phone:
                        st.write(f"**Phone:** {selected_assessment.contact_phone}")
                    if selected_assessment.github:
                        st.write(f"**GitHub:** {selected_assessment.github}")
            
            # Expandable sections for detailed analysis
            
            # Work Experience Timeline
            if selected_assessment.positions:
                with st.expander("💼 Work Experience Timeline", expanded=True):
                    st.markdown("#### Career History")
                    
                    # Sort positions by start date (most recent first)
                    sorted_positions = sorted(
                        [p for p in selected_assessment.positions if p.start_date],
                        key=lambda x: x.start_date,
                        reverse=True
                    )
                    
                    for i, position in enumerate(sorted_positions):
                        # Position header
                        col1, col2 = st.columns([3, 1])
                        with col1:
                            st.markdown(f"**{position.title}** at **{position.company}**")
                        with col2:
                            if position.duration_months:
                                duration_text = f"{position.duration_months} months"
                                if position.duration_months >= 12:
                                    years = position.duration_months // 12
                                    months = position.duration_months % 12
                                    duration_text = f"{years}y {months}m" if months > 0 else f"{years}y"
                                st.write(f"**Duration:** {duration_text}")
                        
                        # Date range
                        start_date = position.start_date.strftime("%b %Y") if position.start_date else "Unknown"
                        end_date = position.end_date.strftime("%b %Y") if position.end_date else "Present"
                        st.write(f"📅 {start_date} - {end_date}")
                        
                        # Skills used
                        if position.skills_used:
                            st.write(f"🔧 **Skills:** {', '.join(position.skills_used)}")
                        
                        # Description
                        if position.description:
                            st.write(f"📝 {position.description}")
                        
                        if i < len(sorted_positions) - 1:
                            st.markdown("---")
            
            # Skills Analysis
            if selected_assessment.skill_analysis:
                with st.expander("🛠️ Skills Analysis", expanded=True):
                    skill_analysis = selected_assessment.skill_analysis
                    
                    # Skills by context
                    col1, col2 = st.columns(2)
                    with col1:
                        if skill_analysis.professional_skills:
                            st.markdown("**💼 Professional Skills:**")
                            for skill in skill_analysis.professional_skills:
                                st.write(f"• {skill}")
                        
                        if skill_analysis.certification_skills:
                            st.markdown("**🏆 Certification Skills:**")
                            for skill in skill_analysis.certification_skills:
                                st.write(f"• {skill}")
                    
                    with col2:
                        if skill_analysis.recent_skills:
                            st.markdown("**🕒 Recent Skills (Last 5 years):**")
                            for skill in skill_analysis.recent_skills:
                                st.write(f"• {skill}")
                        
                        if skill_analysis.academic_skills:
                            st.markdown("**🎓 Academic Skills:**")
                            for skill in skill_analysis.academic_skills:
                                st.write(f"• {skill}")
                    
                    # Skill timeline
                    if skill_analysis.skill_timeline:
                        st.markdown("**📈 Skill Usage Timeline:**")
                        for skill, positions in skill_analysis.skill_timeline.items():
                            if positions:
                                st.write(f"• **{skill}:** {', '.join(positions)}")
            
            # Employment Gaps
            if selected_assessment.employment_gaps:
                with st.expander("⏳ Employment Gaps", expanded=True):
                    for gap in selected_assessment.employment_gaps:
                        severity_color = {
                            'minor': '🟡',
                            'moderate': '🟠', 
                            'significant': '🔴',
                            'major': '🚨'
                        }.get(gap.severity.value, '⚪')
                        
                        st.write(f"{severity_color} **{gap.severity.value.title()} Gap:** {gap.duration_months} months")
                        st.write(f"📅 {gap.start_date.strftime('%b %Y')} - {gap.end_date.strftime('%b %Y')}")
                        if gap.description:
                            st.write(f"📝 {gap.description}")
                        st.markdown("---")
            
            # Job Stability Analysis
            if selected_assessment.job_stability_analysis:
                with st.expander("📊 Job Stability Analysis", expanded=True):
                    stability = selected_assessment.job_stability_analysis
                    
                    col1, col2, col3 = st.columns(3)
                    with col1:
                        st.metric("Stability Score", f"{stability.stability_score:.2f}")
                    with col2:
                        st.metric("Average Position Duration", f"{stability.average_position_duration_months:.1f} months")
                    with col3:
                        st.metric("Short Positions", stability.short_positions_count)
                    
                    st.write(f"**Career Trend:** {stability.career_progression_trend.title()}")
                    st.write(f"**Job Hopping Score:** {stability.job_hopping_score:.2f} (lower is better)")
            
            # Detailed Match Scores
            if selected_assessment.detailed_match_scores:
                with st.expander("🎯 Detailed Match Scores", expanded=False):
                    scores = selected_assessment.detailed_match_scores
                    
                    # Create a bar chart for detailed scores
                    score_names = list(scores.keys())
                    score_values = list(scores.values())
                    
                    fig_detailed = px.bar(
                        x=score_names,
                        y=score_values,
                        title="Detailed Match Score Breakdown",
                        labels={'x': 'Score Category', 'y': 'Score'},
                        color=score_values,
                        color_continuous_scale='RdYlGn'
                    )
                    fig_detailed.update_layout(showlegend=False)
                    st.plotly_chart(fig_detailed, use_container_width=True)
            
            # Summary and Assessment
            if selected_assessment.summary:
                with st.expander("📋 AI Assessment Summary", expanded=True):
                    st.write(selected_assessment.summary)
            
            # Processing metadata
            with st.expander("🔍 Processing Details", expanded=False):
                col1, col2 = st.columns(2)
                with col1:
                    st.write(f"**Processing Status:** {selected_assessment.processing_status.value}")
                    st.write(f"**Analysis Timestamp:** {selected_assessment.analysis_timestamp.strftime('%Y-%m-%d %H:%M:%S') if selected_assessment.analysis_timestamp else 'N/A'}")
                with col2:
                    st.write(f"**Processing Time:** {selected_assessment.processing_time_seconds:.2f}s" if selected_assessment.processing_time_seconds else 'N/A')
                    st.write(f"**Confidence Score:** {selected_assessment.confidence_score:.2f}" if hasattr(selected_assessment, 'confidence_score') else 'N/A')
    
    # Tab 4: Red Flags Analysis
    with tab4:
        st.markdown("### 🚩 Red Flags Analysis")
        
        # Collect all red flags
        all_red_flags = []
        candidate_red_flags = {}
        
        for assessment in successful_assessments:
            candidate_name = assessment.candidate_name or 'Unknown'
            candidate_red_flags[candidate_name] = assessment.red_flags
            all_red_flags.extend(assessment.red_flags)
        
        if not all_red_flags:
            st.success("🎉 Excellent! No red flags found across all candidates.")
            return
        
        # Red flags summary
        st.markdown("#### Summary Statistics")
        col1, col2, col3, col4 = st.columns(4)
        
        critical_count = len([f for f in all_red_flags if f.severity.value == 'critical'])
        high_count = len([f for f in all_red_flags if f.severity.value == 'high'])
        medium_count = len([f for f in all_red_flags if f.severity.value == 'medium'])
        low_count = len([f for f in all_red_flags if f.severity.value == 'low'])
        
        with col1:
            st.metric("Critical", critical_count, delta="🚨" if critical_count > 0 else None)
        with col2:
            st.metric("High", high_count, delta="🔴" if high_count > 0 else None)
        with col3:
            st.metric("Medium", medium_count, delta="🟠" if medium_count > 0 else None)
        with col4:
            st.metric("Low", low_count, delta="🟡" if low_count > 0 else None)
        
        # Red flags by candidate
        st.markdown("#### Red Flags by Candidate")
        
        for candidate_name, red_flags in candidate_red_flags.items():
            if red_flags:
                # Count by severity
                candidate_critical = len([f for f in red_flags if f.severity.value == 'critical'])
                candidate_high = len([f for f in red_flags if f.severity.value == 'high'])
                candidate_medium = len([f for f in red_flags if f.severity.value == 'medium'])
                candidate_low = len([f for f in red_flags if f.severity.value == 'low'])
                
                # Determine overall risk level
                if candidate_critical > 0:
                    risk_level = "🚨 Critical Risk"
                    risk_color = "red"
                elif candidate_high > 0:
                    risk_level = "🔴 High Risk"
                    risk_color = "orange"
                elif candidate_medium > 0:
                    risk_level = "🟠 Medium Risk"
                    risk_color = "yellow"
                else:
                    risk_level = "🟡 Low Risk"
                    risk_color = "green"
                
                with st.expander(f"{candidate_name} - {risk_level} ({len(red_flags)} flags)", expanded=candidate_critical > 0):
                    # Summary counts
                    if candidate_critical + candidate_high + candidate_medium + candidate_low > 0:
                        summary_col1, summary_col2, summary_col3, summary_col4 = st.columns(4)
                        with summary_col1:
                            if candidate_critical > 0:
                                st.write(f"🚨 Critical: {candidate_critical}")
                        with summary_col2:
                            if candidate_high > 0:
                                st.write(f"🔴 High: {candidate_high}")
                        with summary_col3:
                            if candidate_medium > 0:
                                st.write(f"🟠 Medium: {candidate_medium}")
                        with summary_col4:
                            if candidate_low > 0:
                                st.write(f"🟡 Low: {candidate_low}")
                    
                    # Detailed red flags
                    st.markdown("**Detailed Issues:**")
                    
                    # Group by severity for better organization
                    severity_groups = {
                        'critical': [f for f in red_flags if f.severity.value == 'critical'],
                        'high': [f for f in red_flags if f.severity.value == 'high'],
                        'medium': [f for f in red_flags if f.severity.value == 'medium'],
                        'low': [f for f in red_flags if f.severity.value == 'low']
                    }
                    
                    for severity, flags in severity_groups.items():
                        if flags:
                            severity_icon = {'critical': '🚨', 'high': '🔴', 'medium': '🟠', 'low': '🟡'}[severity]
                            st.markdown(f"**{severity_icon} {severity.title()} Issues:**")
                            
                            for flag in flags:
                                st.write(f"• **{flag.type.value.replace('_', ' ').title()}:** {flag.description}")
                                
                                # Show additional details if available
                                if flag.details:
                                    details_text = []
                                    for key, value in flag.details.items():
                                        if key != 'description':  # Avoid duplication
                                            details_text.append(f"{key}: {value}")
                                    if details_text:
                                        st.write(f"  *Details: {', '.join(details_text)}*")
                                
                                if flag.position_related:
                                    st.write(f"  *Related to position: {flag.position_related}*")
        
        # Red flags trends and insights
        st.markdown("#### Insights and Recommendations")
        
        # Most common red flag types
        from collections import Counter
        flag_type_counts = Counter([f.type.value for f in all_red_flags])
        most_common_flags = flag_type_counts.most_common(3)
        
        if most_common_flags:
            st.write("**Most Common Issues:**")
            for flag_type, count in most_common_flags:
                percentage = (count / len(all_red_flags)) * 100
                st.write(f"• {flag_type.replace('_', ' ').title()}: {count} occurrences ({percentage:.1f}% of all flags)")
        
        # Recommendations based on red flags
        recommendations = []
        
        if critical_count > 0:
            recommendations.append("🚨 **Critical Action Required:** Review candidates with critical red flags immediately")
        
        if flag_type_counts.get('employment_gap', 0) > len(successful_assessments) * 0.3:
            recommendations.append("⏳ **Employment Gaps:** Consider if gaps are acceptable for your role requirements")
        
        if flag_type_counts.get('job_hopping', 0) > 0:
            recommendations.append("🔄 **Job Stability:** Evaluate if frequent job changes align with role expectations")
        
        if flag_type_counts.get('missing_required_skill', 0) > 0:
            recommendations.append("🎯 **Skills Gap:** Consider training opportunities or adjust requirements")
        
        if recommendations:
            st.markdown("**Recommendations:**")
            for rec in recommendations:
                st.write(rec)
    
    # Export functionality section
    st.markdown("---")
    st.markdown('<div class="section-header">📥 Export Results</div>', unsafe_allow_html=True)
    render_export_section(batch_result)


def render_export_section(batch_result):
    """
    Render export functionality section with multiple format options.
    
    Args:
        batch_result: BatchProcessingResult containing all analysis results
    """
    if not batch_result.assessments:
        st.warning("⚠️ No data available for export.")
        return
    
    # Export configuration
    st.markdown("### 🔧 Export Configuration")
    
    col1, col2 = st.columns(2)
    
    with col1:
        export_format = st.selectbox(
            "Export Format",
            options=['excel', 'csv', 'json'],
            index=0,
            help="Choose the format for your export file"
        )
        
        filename_prefix = st.text_input(
            "Filename Prefix",
            value="cv_analysis",
            help="Prefix for the exported filename"
        )
    
    with col2:
        include_red_flags = st.checkbox("Include Red Flags Analysis", value=True)
        include_skill_analysis = st.checkbox("Include Skill Analysis", value=True)
        include_position_details = st.checkbox("Include Position Details", value=True)
        include_processing_metadata = st.checkbox("Include Processing Metadata", value=False)
    
    # Create export configuration
    export_config = ExportConfig(
        include_red_flags=include_red_flags,
        include_skill_analysis=include_skill_analysis,
        include_position_details=include_position_details,
        include_processing_metadata=include_processing_metadata,
        format=export_format,
        filename_prefix=filename_prefix
    )
    
    # Export preview
    st.markdown("### 📋 Export Preview")
    
    # Show what will be included
    export_items = []
    if export_format == 'excel':
        export_items.append("📊 Summary Overview Sheet")
        export_items.append("👥 Detailed Candidates Sheet")
        if include_skill_analysis:
            export_items.append("🔧 Skills Analysis Sheet")
        if include_red_flags:
            export_items.append("🚩 Red Flags Sheet")
        if include_position_details:
            export_items.append("💼 Position History Sheet")
        if include_processing_metadata:
            export_items.append("⚙️ Processing Metadata Sheet")
    elif export_format == 'csv':
        export_items.append("📄 Candidates Data (CSV format)")
    else:  # json
        export_items.append("📄 Complete Analysis Data (JSON format)")
    
    st.write("**Export will include:**")
    for item in export_items:
        st.write(f"• {item}")
    
    # Export statistics
    col1, col2, col3, col4 = st.columns(4)
    with col1:
        st.metric("Total Candidates", len(batch_result.assessments))
    with col2:
        st.metric("Successful Analyses", batch_result.successful_count)
    with col3:
        total_red_flags = sum(len(a.red_flags) for a in batch_result.assessments)
        st.metric("Total Red Flags", total_red_flags)
    with col4:
        avg_match_score = sum(a.match_score for a in batch_result.assessments if a.match_score) / max(1, len([a for a in batch_result.assessments if a.match_score]))
        st.metric("Avg Match Score", f"{avg_match_score:.1%}")
    
    # Export buttons
    st.markdown("### 📥 Download Options")
    
    col1, col2, col3 = st.columns(3)
    
    with col1:
        if st.button("🚀 Generate Export", type="primary", use_container_width=True):
            try:
                with st.spinner(f"Generating {export_format.upper()} export..."):
                    export_service = ExportService()
                    
                    # Generate export
                    export_result = export_service.export_batch_results(
                        batch_result=batch_result,
                        config=export_config
                    )
                    
                    # Store in session state for download
                    st.session_state.export_data = export_result
                    st.success(f"✅ Export generated successfully! ({export_result['file_size']} bytes)")
                    
                    # Show export details
                    with st.expander("📊 Export Details", expanded=True):
                        st.write(f"**Format:** {export_result['format'].upper()}")
                        st.write(f"**Records Exported:** {export_result['records_exported']}")
                        st.write(f"**File Size:** {export_result['file_size']:,} bytes")
                        st.write(f"**Generated:** {export_result['export_timestamp']}")
                        
                        if export_format == 'excel' and 'sheets_created' in export_result:
                            st.write(f"**Sheets Created:** {', '.join(export_result['sheets_created'])}")
            
            except Exception as e:
                st.error(f"❌ Export failed: {str(e)}")
                logger.error(f"Export error: {e}")
    
    with col2:
        # Download button (only show if export data exists)
        if hasattr(st.session_state, 'export_data') and st.session_state.export_data:
            export_data = st.session_state.export_data
            
            # Generate filename with timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"{export_config.filename_prefix}_{timestamp}.{export_data['format']}"
            
            # Determine MIME type
            mime_types = {
                'excel': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'csv': 'text/csv',
                'json': 'application/json'
            }
            
            st.download_button(
                label=f"📥 Download {export_data['format'].upper()}",
                data=export_data['file_data'],
                file_name=filename,
                mime=mime_types.get(export_data['format'], 'application/octet-stream'),
                use_container_width=True
            )
        else:
            st.button("📥 Download File", disabled=True, use_container_width=True, 
                     help="Generate export first")
    
    with col3:
        if st.button("📊 Generate Report", use_container_width=True):
            try:
                with st.spinner("Generating comprehensive report..."):
                    export_service = ExportService()
                    
                    # Generate report
                    report = export_service.generate_export_report(
                        batch_result=batch_result,
                        config=export_config
                    )
                    
                    # Display report
                    st.markdown("### 📊 Analysis Report")
                    
                    # Export summary
                    summary = report['export_summary']
                    st.write(f"**Analysis completed:** {summary['export_timestamp']}")
                    st.write(f"**Processing time:** {summary['processing_time_seconds']:.2f} seconds")
                    
                    # Candidate statistics
                    stats = report['candidate_statistics']
                    col1, col2 = st.columns(2)
                    with col1:
                        st.metric("Average Match Score", f"{stats['average_match_score']:.1%}")
                        st.metric("Highest Match Score", f"{stats['highest_match_score']:.1%}")
                    with col2:
                        st.metric("Average Experience", f"{stats['average_experience_years']:.1f} years")
                        st.metric("Critical Issues", stats['candidates_with_critical_issues'])
                    
                    # Top candidates
                    if report['top_candidates']:
                        st.markdown("**🏆 Top Candidates:**")
                        for i, candidate in enumerate(report['top_candidates'], 1):
                            st.write(f"{i}. **{candidate['name']}** - {candidate['match_score']:.1%} match, {candidate['experience_years']} years exp")
                    
                    # Most common skills
                    if report['most_common_skills']:
                        st.markdown("**🔧 Most Common Skills:**")
                        for skill_data in report['most_common_skills'][:5]:
                            st.write(f"• **{skill_data['skill']}**: {skill_data['frequency']} candidates")
            
            except Exception as e:
                st.error(f"❌ Report generation failed: {str(e)}")
                logger.error(f"Report generation error: {e}")
    
    # Additional export options
    with st.expander("🔧 Advanced Export Options", expanded=False):
        st.markdown("### Charts and Visualizations")
        
        if st.button("📈 Generate Charts for Export"):
            try:
                with st.spinner("Creating charts..."):
                    export_service = ExportService()
                    charts = export_service.create_charts_for_export(batch_result)
                    
                    if charts:
                        st.success(f"✅ Generated {len(charts)} charts")
                        
                        # Display charts
                        for chart_name, chart_data in charts.items():
                            st.markdown(f"**{chart_name.replace('_', ' ').title()}**")
                            st.image(chart_data)
                    else:
                        st.info("No charts could be generated from the current data.")
            
            except Exception as e:
                st.error(f"❌ Chart generation failed: {str(e)}")
        
        st.markdown("### Batch Export Settings")
        st.info("💡 **Tip:** For large datasets, consider exporting in smaller batches or using CSV format for better performance.")
        
        # Clear export data button
        if st.button("🗑️ Clear Export Data"):
            if hasattr(st.session_state, 'export_data'):
                del st.session_state.export_data
            st.success("Export data cleared from session.")


def main():
    """Enhanced main application function with comprehensive UI and integrated system management."""
    try:
        # Initialize session state
        initialize_session_state()
        
        # Initialize integrated system on first run
        if not st.session_state.get('system_initialized', False):
            with st.spinner("🔧 Initializing CV Analyzer system..."):
                from integration_manager import get_integration_manager, initialize_integrated_system

                # Create default configuration for system initialization
                default_config = AnalysisConfig()

                # Initialize the integrated system
                integration_mgr = get_integration_manager()

                # Ensure integration manager is not None
                if integration_mgr is None:
                    st.error("❌ Failed to get integration manager instance.")
                    st.stop()

                system_initialized = initialize_integrated_system(default_config)

                if system_initialized:
                    st.session_state.system_initialized = True
                    st.session_state.integration_manager = integration_mgr
                    logger.info("Integrated system initialized successfully")
                else:
                    st.error("❌ Failed to initialize system. Please check configuration and try again.")
                    st.stop()
        
        # Render sidebar components
        render_system_status()
        render_session_management_sidebar()
        render_progress_tracking()
        render_help_section()
        
        # Render main header
        render_header()
        
        # Show system status if available
        if 'integration_manager' in st.session_state and st.session_state.integration_manager is not None:
            try:
                system_status = st.session_state.integration_manager.get_system_status()
                if not all(system_status['health_check'].values()):
                    st.warning("⚠️ Some system components may not be functioning optimally. Check the system status in the sidebar.")
            except Exception as e:
                logger.warning(f"Could not get system status: {e}")
                st.warning("⚠️ System status check failed. Some components may not be functioning optimally.")
        
        # Create tabs for different sections
        tab1, tab2, tab3, tab4 = st.tabs([
            "📄 Analysis", 
            "⚙️ Configuration", 
            "📊 History", 
            "📝 Logs"
        ])
        
        with tab1:
            # Main content area
            with st.container():
                # File upload section
                file_data = render_upload_section()
                
                # Job description section
                job_description = render_job_description_input()
                
                # Configuration panel section
                analysis_config = render_configuration_panel()
                
                # Validation with enhanced error handling
                is_valid, errors = validate_inputs(file_data, job_description)
                render_validation_messages(is_valid, errors)
                
                # Analysis button with enhanced features
                start_analysis = render_analysis_button(is_valid, file_data, job_description, analysis_config)
                
                # Processing status with real-time updates
                render_processing_status()
            
                # Start analysis if requested
                if start_analysis:
                    try:
                        # Validate system is ready
                        if not st.session_state.get('system_initialized', False):
                            st.error("❌ System not properly initialized. Please refresh the page.")
                            st.stop()
                        
                        # Create new session
                        file_names = [f['name'] for f in file_data]
                        session_id = session_manager.create_new_session(
                            job_description, 
                            analysis_config, 
                            file_names
                        )
                        
                        # Store file data in session state
                        st.session_state.uploaded_files_data = file_data
                        
                        # Show analysis confirmation with system info
                        st.info(f"🚀 Starting integrated analysis of {len(file_data)} CV(s)... (Session: {session_id[:8]})")
                        
                        # Show system readiness check
                        integration_mgr = st.session_state.integration_manager
                        if integration_mgr is None:
                            st.error("❌ Integration manager not available. Please refresh the page.")
                            st.stop()

                        system_status = integration_mgr.get_system_status()
                        
                        if not system_status['initialized']:
                            st.error("❌ System not initialized. Please refresh the page.")
                            st.stop()
                        
                        # Run integrated analysis with comprehensive error handling
                        with st.spinner("🔄 Running integrated CV analysis..."):
                            try:
                                asyncio.run(run_analysis_with_session(
                                    file_data, 
                                    job_description, 
                                    analysis_config,
                                    session_id
                                ))
                                
                                # Show system performance metrics after analysis
                                if 'performance_metrics' in system_status:
                                    metrics = system_status['performance_metrics']
                                    if metrics:
                                        st.info(f"📊 System Performance: {metrics.get('memory_usage_mb', 0):.1f}MB memory used")
                                
                            except Exception as e:
                                st.error(f"❌ Integrated analysis failed: {str(e)}")
                                session_manager.add_error(str(e), {
                                    'context': 'integrated_analysis', 
                                    'session_id': session_id,
                                    'system_status': system_status
                                })
                                logger.error(f"Integrated analysis error: {e}")
                                
                                # Show system diagnostics on error
                                with st.expander("🔧 System Diagnostics", expanded=False):
                                    st.json(system_status)
                    
                    except Exception as e:
                        st.error(f"❌ Failed to start integrated analysis: {str(e)}")
                        logger.error(f"Failed to start integrated analysis: {e}")
                
                # Display results dashboard
                if st.session_state.current_analysis_results:
                    render_results_dashboard(st.session_state.current_analysis_results)
        
        with tab2:
            render_configuration_management()
            
            # Add system configuration section
            st.markdown("### 🔧 System Configuration")
            
            if 'integration_manager' in st.session_state and st.session_state.integration_manager is not None:
                try:
                    system_status = st.session_state.integration_manager.get_system_status()
                except Exception as e:
                    st.error(f"❌ Could not get system status: {e}")
                    system_status = None
                
                if system_status:
                    # Show component status
                    st.markdown("#### Component Status")
                    for component_name, component_info in system_status.get('components', {}).items():
                        status_icon = "✅" if component_info['loaded'] else "❌"
                        st.write(f"{status_icon} **{component_name}**: {component_info['type']}")

                    # Show health check results
                    st.markdown("#### Health Check")
                    health_checks = system_status.get('health_check', {})
                    for check_name, is_healthy in health_checks.items():
                        status_icon = "✅" if is_healthy else "❌"
                        st.write(f"{status_icon} {check_name.replace('_', ' ').title()}")
                else:
                    st.warning("System status not available")
        
        with tab3:
            render_session_history()
        
        with tab4:
            render_processing_logs()
            
            # Add system logs section
            if 'integration_manager' in st.session_state:
                st.markdown("### 🔧 System Logs")
                
                # Show recent system events
                st.info("System logs are integrated with the main processing logs above.")
        
        # Render footer
        render_footer()
        
    except Exception as e:
        st.error(f"❌ Application error: {str(e)}")
        logger.error(f"Main application error: {e}")
        
        # Show comprehensive error details
        with st.expander("🔍 Error Details", expanded=False):
            st.code(str(e))
            
            # Show system status if available
            if 'integration_manager' in st.session_state and st.session_state.integration_manager is not None:
                try:
                    system_status = st.session_state.integration_manager.get_system_status()
                    st.json(system_status)
                except Exception:
                    st.write("Could not retrieve system status")
            
            st.write("Please refresh the page or contact support if the issue persists.")


if __name__ == "__main__":
    main()