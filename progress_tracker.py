"""
Progress Tracking System for CV Analyzer Enhancement.

This module provides real-time progress tracking, status updates, time estimation,
and cancellation functionality for long-running analysis tasks.
"""

import streamlit as st
import time
import threading
from typing import Dict, List, Optional, Callable, Any
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
import logging
import asyncio
from concurrent.futures import ThreadPoolExecutor

logger = logging.getLogger(__name__)


class TaskStatus(Enum):
    """Task execution status."""
    PENDING = "pending"
    RUNNING = "running"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


@dataclass
class ProgressStep:
    """Represents a single step in the progress tracking."""
    name: str
    description: str
    weight: float = 1.0  # Relative weight for progress calculation
    status: TaskStatus = TaskStatus.PENDING
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    error_message: Optional[str] = None
    substeps: List['ProgressStep'] = field(default_factory=list)


@dataclass
class ProgressMetrics:
    """Progress tracking metrics and statistics."""
    total_steps: int
    completed_steps: int
    failed_steps: int
    current_step: Optional[str]
    overall_progress: float  # 0.0 to 1.0
    estimated_time_remaining: Optional[timedelta]
    elapsed_time: timedelta
    average_step_time: float
    throughput: float  # steps per second


class ProgressTracker:
    """
    Comprehensive progress tracking system for CV analysis tasks.
    
    Features:
    - Real-time progress bars and status indicators
    - Estimated time remaining calculations
    - Task cancellation functionality
    - Hierarchical progress tracking (steps and substeps)
    - Performance metrics and throughput monitoring
    """
    
    def __init__(self, task_name: str = "Analysis"):
        """
        Initialize the progress tracker.

        Args:
            task_name: Name of the task being tracked
        """
        self.task_name = task_name
        self.steps: List[ProgressStep] = []
        self.start_time: Optional[datetime] = None
        self.end_time: Optional[datetime] = None
        self.is_cancelled = False
        self.cancellation_callback: Optional[Callable] = None

        # Progress callbacks for external monitoring
        self.progress_callbacks: List[Callable[[Dict[str, Any]], None]] = []

        # Streamlit containers for UI updates
        self.progress_container = None
        self.status_container = None
        self.metrics_container = None

        # Thread safety
        self._lock = threading.Lock()

        # Performance tracking
        self.step_times: List[float] = []
        self.last_update_time = time.time()
    
    def add_step(self, name: str, description: str, weight: float = 1.0, 
                 substeps: List[Dict[str, Any]] = None) -> ProgressStep:
        """
        Add a progress step to track.
        
        Args:
            name: Step identifier
            description: Human-readable description
            weight: Relative weight for progress calculation
            substeps: Optional list of substep definitions
            
        Returns:
            ProgressStep object
        """
        step = ProgressStep(name=name, description=description, weight=weight)
        
        # Add substeps if provided
        if substeps:
            for substep_data in substeps:
                substep = ProgressStep(
                    name=substep_data['name'],
                    description=substep_data['description'],
                    weight=substep_data.get('weight', 1.0)
                )
                step.substeps.append(substep)
        
        with self._lock:
            self.steps.append(step)
        
        logger.debug(f"Added progress step: {name}")
        return step
    
    def start_tracking(self):
        """Start progress tracking."""
        with self._lock:
            self.start_time = datetime.now()
            self.is_cancelled = False
        
        # Initialize UI containers
        self._setup_ui_containers()
        
        logger.info(f"Started progress tracking for: {self.task_name}")
    
    def update_step_status(self, step_name: str, status: TaskStatus, 
                          error_message: str = None):
        """
        Update the status of a specific step.
        
        Args:
            step_name: Name of the step to update
            status: New status
            error_message: Optional error message if status is FAILED
        """
        with self._lock:
            step = self._find_step(step_name)
            if not step:
                logger.warning(f"Step not found: {step_name}")
                return
            
            # Update step status
            old_status = step.status
            step.status = status
            
            # Set timestamps
            if status == TaskStatus.RUNNING and old_status == TaskStatus.PENDING:
                step.start_time = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                step.end_time = datetime.now()
                
                # Record step time for performance metrics
                if step.start_time:
                    step_duration = (step.end_time - step.start_time).total_seconds()
                    self.step_times.append(step_duration)
            
            # Set error message if provided
            if error_message:
                step.error_message = error_message
        
        # Update UI and notify callbacks
        self._update_ui()
        self._notify_progress_callbacks()
        
        logger.debug(f"Updated step {step_name}: {old_status.value} -> {status.value}")
    
    def update_substep_status(self, step_name: str, substep_name: str, 
                             status: TaskStatus, error_message: str = None):
        """
        Update the status of a substep.
        
        Args:
            step_name: Parent step name
            substep_name: Substep name
            status: New status
            error_message: Optional error message
        """
        with self._lock:
            step = self._find_step(step_name)
            if not step:
                return
            
            substep = self._find_substep(step, substep_name)
            if not substep:
                logger.warning(f"Substep not found: {step_name}.{substep_name}")
                return
            
            # Update substep
            substep.status = status
            if status == TaskStatus.RUNNING:
                substep.start_time = datetime.now()
            elif status in [TaskStatus.COMPLETED, TaskStatus.FAILED, TaskStatus.CANCELLED]:
                substep.end_time = datetime.now()
            
            if error_message:
                substep.error_message = error_message
            
            # Update parent step status based on substeps
            self._update_parent_step_status(step)
        
        # Update UI and notify callbacks
        self._update_ui()
        self._notify_progress_callbacks()
    
    def get_progress_metrics(self) -> ProgressMetrics:
        """
        Calculate current progress metrics.
        
        Returns:
            ProgressMetrics object with current statistics
        """
        with self._lock:
            total_steps = len(self.steps)
            completed_steps = sum(1 for step in self.steps if step.status == TaskStatus.COMPLETED)
            failed_steps = sum(1 for step in self.steps if step.status == TaskStatus.FAILED)
            
            # Find current step
            current_step = None
            for step in self.steps:
                if step.status == TaskStatus.RUNNING:
                    current_step = step.name
                    break
            
            # Calculate overall progress (weighted)
            total_weight = sum(step.weight for step in self.steps)
            completed_weight = sum(
                step.weight for step in self.steps 
                if step.status == TaskStatus.COMPLETED
            )
            
            # Add partial progress for running steps
            for step in self.steps:
                if step.status == TaskStatus.RUNNING and step.substeps:
                    substep_progress = self._calculate_substep_progress(step)
                    completed_weight += step.weight * substep_progress
            
            overall_progress = completed_weight / total_weight if total_weight > 0 else 0.0
            
            # Calculate elapsed time
            elapsed_time = timedelta()
            if self.start_time:
                end_time = self.end_time or datetime.now()
                elapsed_time = end_time - self.start_time
            
            # Calculate average step time and estimate remaining time
            average_step_time = sum(self.step_times) / len(self.step_times) if self.step_times else 0.0
            
            estimated_time_remaining = None
            if average_step_time > 0 and overall_progress > 0:
                remaining_progress = 1.0 - overall_progress
                estimated_seconds = (elapsed_time.total_seconds() / overall_progress) * remaining_progress
                estimated_time_remaining = timedelta(seconds=estimated_seconds)
            
            # Calculate throughput
            throughput = completed_steps / elapsed_time.total_seconds() if elapsed_time.total_seconds() > 0 else 0.0
            
            return ProgressMetrics(
                total_steps=total_steps,
                completed_steps=completed_steps,
                failed_steps=failed_steps,
                current_step=current_step,
                overall_progress=overall_progress,
                estimated_time_remaining=estimated_time_remaining,
                elapsed_time=elapsed_time,
                average_step_time=average_step_time,
                throughput=throughput
            )
    
    def cancel_task(self):
        """Cancel the current task."""
        with self._lock:
            self.is_cancelled = True
            
            # Mark all pending/running steps as cancelled
            for step in self.steps:
                if step.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                    step.status = TaskStatus.CANCELLED
                    step.end_time = datetime.now()
                
                # Cancel substeps too
                for substep in step.substeps:
                    if substep.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                        substep.status = TaskStatus.CANCELLED
                        substep.end_time = datetime.now()
        
        # Call cancellation callback if provided
        if self.cancellation_callback:
            try:
                self.cancellation_callback()
            except Exception as e:
                logger.error(f"Error in cancellation callback: {e}")
        
        # Update UI
        self._update_ui()
        
        logger.info(f"Task cancelled: {self.task_name}")
    
    def set_cancellation_callback(self, callback: Callable):
        """
        Set a callback function to be called when task is cancelled.

        Args:
            callback: Function to call on cancellation
        """
        self.cancellation_callback = callback

    def add_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Add a progress callback function.

        Args:
            callback: Function to call when progress is updated
        """
        with self._lock:
            self.progress_callbacks.append(callback)

    def remove_progress_callback(self, callback: Callable[[Dict[str, Any]], None]):
        """
        Remove a progress callback function.

        Args:
            callback: Function to remove from callbacks
        """
        with self._lock:
            if callback in self.progress_callbacks:
                self.progress_callbacks.remove(callback)

    def _notify_progress_callbacks(self):
        """Notify all progress callbacks with current progress data."""
        if not self.progress_callbacks:
            return

        try:
            # Get current progress metrics
            metrics = self.get_progress_metrics()

            # Create progress data dictionary
            progress_data = {
                'overall_progress': metrics.overall_progress,
                'current_step': metrics.current_step,
                'completed_steps': metrics.completed_steps,
                'total_steps': metrics.total_steps,
                'elapsed_time': metrics.elapsed_time.total_seconds(),
                'estimated_time_remaining': metrics.estimated_time_remaining.total_seconds() if metrics.estimated_time_remaining else None,
                'task_name': self.task_name,
                'is_cancelled': self.is_cancelled
            }

            # Notify all callbacks
            for callback in self.progress_callbacks:
                try:
                    callback(progress_data)
                except Exception as e:
                    logger.error(f"Error in progress callback: {e}")
        except Exception as e:
            logger.error(f"Error notifying progress callbacks: {e}")
    
    def is_task_cancelled(self) -> bool:
        """Check if the task has been cancelled."""
        return self.is_cancelled
    
    def finish_tracking(self, status: TaskStatus = TaskStatus.COMPLETED):
        """
        Finish progress tracking.
        
        Args:
            status: Final status of the task
        """
        with self._lock:
            self.end_time = datetime.now()
            
            # Mark any remaining steps as completed or failed
            for step in self.steps:
                if step.status in [TaskStatus.PENDING, TaskStatus.RUNNING]:
                    step.status = status
                    step.end_time = self.end_time
        
        # Final UI update
        self._update_ui()
        
        logger.info(f"Finished progress tracking for: {self.task_name} ({status.value})")
    
    def _setup_ui_containers(self):
        """Set up Streamlit containers for progress display."""
        if 'progress_tracker_containers' not in st.session_state:
            st.session_state.progress_tracker_containers = {}
        
        # Create containers for this tracker
        container_key = f"tracker_{self.task_name}"
        
        if container_key not in st.session_state.progress_tracker_containers:
            # Create main progress container
            progress_placeholder = st.empty()
            
            with progress_placeholder.container():
                st.markdown(f"### 🔄 {self.task_name} Progress")
                
                # Overall progress bar
                self.progress_container = st.empty()
                
                # Status and metrics
                col1, col2 = st.columns(2)
                with col1:
                    self.status_container = st.empty()
                with col2:
                    self.metrics_container = st.empty()
                
                # Cancellation button
                if st.button("❌ Cancel Task", key=f"cancel_{self.task_name}"):
                    self.cancel_task()
            
            st.session_state.progress_tracker_containers[container_key] = {
                'placeholder': progress_placeholder,
                'progress': self.progress_container,
                'status': self.status_container,
                'metrics': self.metrics_container
            }
        else:
            # Reuse existing containers
            containers = st.session_state.progress_tracker_containers[container_key]
            self.progress_container = containers['progress']
            self.status_container = containers['status']
            self.metrics_container = containers['metrics']
    
    def _update_ui(self):
        """Update the Streamlit UI with current progress."""
        if not self.progress_container:
            return
        
        try:
            metrics = self.get_progress_metrics()
            
            # Update progress bar
            with self.progress_container:
                st.progress(metrics.overall_progress)
                
                # Progress details
                progress_text = f"**Progress:** {metrics.completed_steps}/{metrics.total_steps} steps completed ({metrics.overall_progress:.1%})"
                if metrics.current_step:
                    progress_text += f"\n\n**Current Step:** {metrics.current_step}"
                
                st.markdown(progress_text)
            
            # Update status
            with self.status_container:
                st.markdown("**📊 Status**")
                
                # Status indicators
                for step in self.steps:
                    status_icon = {
                        TaskStatus.PENDING: "⏳",
                        TaskStatus.RUNNING: "🔄",
                        TaskStatus.COMPLETED: "✅",
                        TaskStatus.FAILED: "❌",
                        TaskStatus.CANCELLED: "🚫"
                    }
                    
                    icon = status_icon.get(step.status, "❓")
                    st.write(f"{icon} {step.description}")
                    
                    # Show substeps if any
                    if step.substeps:
                        for substep in step.substeps:
                            substep_icon = status_icon.get(substep.status, "❓")
                            st.write(f"  └ {substep_icon} {substep.description}")
            
            # Update metrics
            with self.metrics_container:
                st.markdown("**⏱️ Metrics**")
                
                # Elapsed time
                elapsed_str = str(metrics.elapsed_time).split('.')[0]  # Remove microseconds
                st.write(f"**Elapsed:** {elapsed_str}")
                
                # Estimated time remaining
                if metrics.estimated_time_remaining:
                    remaining_str = str(metrics.estimated_time_remaining).split('.')[0]
                    st.write(f"**Remaining:** ~{remaining_str}")
                
                # Throughput
                if metrics.throughput > 0:
                    st.write(f"**Throughput:** {metrics.throughput:.2f} steps/sec")
                
                # Average step time
                if metrics.average_step_time > 0:
                    st.write(f"**Avg Step Time:** {metrics.average_step_time:.1f}s")
        
        except Exception as e:
            logger.error(f"Error updating progress UI: {e}")
    
    def _find_step(self, step_name: str) -> Optional[ProgressStep]:
        """Find a step by name."""
        for step in self.steps:
            if step.name == step_name:
                return step
        return None
    
    def _find_substep(self, step: ProgressStep, substep_name: str) -> Optional[ProgressStep]:
        """Find a substep within a step."""
        for substep in step.substeps:
            if substep.name == substep_name:
                return substep
        return None
    
    def _calculate_substep_progress(self, step: ProgressStep) -> float:
        """Calculate progress for a step based on its substeps."""
        if not step.substeps:
            return 0.0
        
        total_weight = sum(substep.weight for substep in step.substeps)
        completed_weight = sum(
            substep.weight for substep in step.substeps 
            if substep.status == TaskStatus.COMPLETED
        )
        
        return completed_weight / total_weight if total_weight > 0 else 0.0
    
    def _update_parent_step_status(self, step: ProgressStep):
        """Update parent step status based on substep completion."""
        if not step.substeps:
            return
        
        # Check if all substeps are completed
        all_completed = all(substep.status == TaskStatus.COMPLETED for substep in step.substeps)
        any_failed = any(substep.status == TaskStatus.FAILED for substep in step.substeps)
        any_cancelled = any(substep.status == TaskStatus.CANCELLED for substep in step.substeps)
        any_running = any(substep.status == TaskStatus.RUNNING for substep in step.substeps)
        
        if any_cancelled:
            step.status = TaskStatus.CANCELLED
        elif any_failed:
            step.status = TaskStatus.FAILED
        elif all_completed:
            step.status = TaskStatus.COMPLETED
        elif any_running:
            step.status = TaskStatus.RUNNING


class CVAnalysisProgressTracker(ProgressTracker):
    """
    Specialized progress tracker for CV analysis tasks.
    
    Provides predefined steps and progress tracking specifically
    tailored for the CV analysis workflow.
    """
    
    def __init__(self, num_cvs: int):
        """
        Initialize CV analysis progress tracker.
        
        Args:
            num_cvs: Number of CVs to be processed
        """
        super().__init__(f"CV Analysis ({num_cvs} files)")
        self.num_cvs = num_cvs
        
        # Add standard CV analysis steps
        self._setup_analysis_steps()
    
    def _setup_analysis_steps(self):
        """Set up standard steps for CV analysis."""
        
        # Step 1: Initialization
        self.add_step(
            "initialization",
            "Initializing analysis engine",
            weight=0.5
        )
        
        # Step 2: PDF Processing
        self.add_step(
            "pdf_processing",
            f"Processing {self.num_cvs} PDF files",
            weight=2.0,
            substeps=[
                {"name": f"pdf_{i}", "description": f"Processing CV {i+1}", "weight": 1.0}
                for i in range(self.num_cvs)
            ]
        )
        
        # Step 3: LLM Analysis
        self.add_step(
            "llm_analysis",
            "Running AI analysis",
            weight=3.0,
            substeps=[
                {"name": f"llm_{i}", "description": f"Analyzing CV {i+1}", "weight": 1.0}
                for i in range(self.num_cvs)
            ]
        )
        
        # Step 4: Career Analysis
        self.add_step(
            "career_analysis",
            "Performing career analysis",
            weight=2.0,
            substeps=[
                {"name": "gap_detection", "description": "Detecting employment gaps", "weight": 1.0},
                {"name": "stability_analysis", "description": "Analyzing job stability", "weight": 1.0},
                {"name": "skill_classification", "description": "Classifying skills", "weight": 1.0}
            ]
        )
        
        # Step 5: Results Generation
        self.add_step(
            "results_generation",
            "Generating results and reports",
            weight=1.0
        )
    
    def update_cv_processing(self, cv_index: int, status: TaskStatus, error_message: str = None):
        """Update progress for a specific CV processing."""
        self.update_substep_status("pdf_processing", f"pdf_{cv_index}", status, error_message)
    
    def update_cv_analysis(self, cv_index: int, status: TaskStatus, error_message: str = None):
        """Update progress for a specific CV analysis."""
        self.update_substep_status("llm_analysis", f"llm_{cv_index}", status, error_message)


# Global progress tracker instance
current_progress_tracker: Optional[ProgressTracker] = None


def get_current_tracker() -> Optional[ProgressTracker]:
    """Get the current active progress tracker."""
    return current_progress_tracker


def set_current_tracker(tracker: ProgressTracker):
    """Set the current active progress tracker."""
    global current_progress_tracker
    current_progress_tracker = tracker


def clear_current_tracker():
    """Clear the current progress tracker."""
    global current_progress_tracker
    current_progress_tracker = None